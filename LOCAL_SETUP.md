# 🚀 Local Development Setup Guide

This guide will help you run the Weight Loss Dashboard on your local machine for development and testing.

## 📋 Prerequisites

### Required Software

1. **PHP 8.0+**
   - **macOS**: `brew install php`
   - **Ubuntu/Debian**: `sudo apt install php8.1 php8.1-mysql php8.1-curl php8.1-json`
   - **Windows**: Download from [php.net](https://www.php.net/downloads) or use XAMPP

2. **MySQL 8.0+**
   - **macOS**: `brew install mysql`
   - **Ubuntu/Debian**: `sudo apt install mysql-server`
   - **Windows**: Download from [mysql.com](https://dev.mysql.com/downloads/) or use XAMPP

3. **Flutter SDK**
   - **All platforms**: Visit [flutter.dev](https://docs.flutter.dev/get-started/install)
   - **macOS**: `brew install --cask flutter`

### Optional (Recommended)
- **Git**: For version control
- **VS Code**: With Flutter and PHP extensions
- **Postman**: For API testing

## 🛠 Quick Start

### Option 1: Automated Setup (Recommended)

1. **Start Backend Server**:
   ```bash
   # macOS/Linux
   ./start-backend.sh
   
   # Windows
   start-backend.bat
   ```

2. **Start Frontend Server** (in a new terminal):
   ```bash
   # macOS/Linux
   ./start-frontend.sh
   
   # Windows
   start-frontend.bat
   ```

3. **Access the Application**:
   - **Setup Wizard**: http://localhost:8000/setup.php
   - **Admin Panel**: http://localhost:8000/admin/
   - **User App**: http://localhost:3000
   - **API**: http://localhost:8000/api/

### Option 2: Manual Setup

#### Backend Setup

1. **Create Database**:
   ```sql
   CREATE DATABASE weight_loss_dashboard;
   ```

2. **Configure Environment**:
   ```bash
   cp backend/.env.example backend/.env
   # Edit backend/.env with your database credentials
   ```

3. **Create Directories**:
   ```bash
   mkdir -p backend/{logs,cache,uploads}
   chmod 755 backend/{logs,cache,uploads}  # Unix/Linux/macOS only
   ```

4. **Start PHP Server**:
   ```bash
   php -S localhost:8000 -t .
   ```

#### Frontend Setup

1. **Install Dependencies**:
   ```bash
   cd frontend
   flutter pub get
   flutter config --enable-web
   ```

2. **Start Flutter Server**:
   ```bash
   flutter run -d web-server --web-port 3000
   ```

## 🔧 Configuration

### Database Setup

1. **Visit Setup Wizard**: http://localhost:8000/setup.php
2. **Configure Database**: Enter your MySQL credentials
3. **Create Tables**: Let the wizard create the database schema
4. **Create Admin Account**: Set up your administrator credentials

### Default Credentials

After setup, you can login to the admin panel with the credentials you created during setup.

## 📱 Testing the Application

### Admin Panel Testing

1. **Login**: http://localhost:8000/admin/
2. **Add a User**:
   - Go to Users → Add New User
   - Fill in user details (name, email, weight, etc.)
   - Assign a course (create one first if needed)
3. **Generate Login Link**:
   - Click the link icon next to a user
   - Copy the generated unique link

### User App Testing

1. **Access User App**: http://localhost:3000
2. **Login**: Paste the unique link token in the login form
3. **Test Features**:
   - View dashboard
   - Check course progress
   - Navigate through the app

### API Testing

Test API endpoints using curl or Postman:

```bash
# Test API health
curl http://localhost:8000/api/

# Test admin login
curl -X POST http://localhost:8000/api/auth/admin-login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"your_password"}'
```

## 🐛 Troubleshooting

### Common Issues

**Backend Server Won't Start**
```bash
# Check if port 8000 is in use
lsof -i :8000  # macOS/Linux
netstat -an | findstr :8000  # Windows

# Use different port if needed
php -S localhost:8080 -t .
```

**Frontend Build Errors**
```bash
# Clean Flutter cache
flutter clean
flutter pub get

# Check Flutter doctor
flutter doctor
```

**Database Connection Failed**
- Verify MySQL is running: `sudo service mysql start` (Linux)
- Check credentials in `backend/.env`
- Ensure database exists: `CREATE DATABASE weight_loss_dashboard;`

**CORS Issues**
- The backend includes CORS headers for localhost:3000
- If using different ports, update `backend/config/config.php`

### Development Tips

**Hot Reload**
- Flutter web supports hot reload: `r` in terminal
- PHP changes are reflected immediately (no restart needed)

**Debugging**
- Check PHP errors in `backend/logs/app.log`
- Use browser dev tools for frontend debugging
- Enable Flutter web debugging: `flutter run -d web-server --debug`

**Database Management**
- Use phpMyAdmin, MySQL Workbench, or command line
- Database schema is in `backend/database/schema.sql`

## 🔄 Development Workflow

1. **Start both servers** using the startup scripts
2. **Make changes** to your code
3. **Test immediately** - changes are reflected automatically
4. **Use browser dev tools** for frontend debugging
5. **Check logs** in `backend/logs/` for backend issues

## 📊 Monitoring

### Log Files
- **Application Logs**: `backend/logs/app.log`
- **PHP Errors**: Check PHP error log or enable display_errors
- **Flutter Console**: Terminal where you ran `flutter run`

### Database
- **User Activity**: Check `user_activity_log` table
- **Sessions**: Monitor `user_sessions` table
- **Progress**: View `user_video_progress` table

## 🚀 Next Steps

Once you have the local setup working:

1. **Add Sample Data**: Create courses and users for testing
2. **Configure Vimeo**: Add your Vimeo API credentials for video features
3. **Customize UI**: Modify Flutter widgets and PHP templates
4. **Test Features**: Try all functionality end-to-end
5. **Deploy**: Follow the main README.md for production deployment

## 📞 Support

If you encounter issues:
1. Check this troubleshooting guide
2. Review the main README.md
3. Check the browser console and server logs
4. Ensure all prerequisites are properly installed

Happy coding! 🎉

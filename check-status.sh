#!/bin/bash

# Weight Loss Dashboard - Server Status Check

echo "🔍 Checking Weight Loss Dashboard Server Status..."
echo "=================================================="

# Check Backend Server
echo "🔧 Backend Server (PHP):"
if curl -s http://localhost:8000/setup.php > /dev/null; then
    echo "   ✅ Backend is running at http://localhost:8000"
    echo "   📋 Setup Wizard: http://localhost:8000/setup.php"
    echo "   👨‍💼 Admin Panel: http://localhost:8000/admin/"
    echo "   🔌 API Base: http://localhost:8000/api/"
else
    echo "   ❌ Backend server is not responding"
    echo "   💡 Run: ./start-backend.sh"
fi

echo ""

# Check Frontend Server
echo "📱 Frontend Server (Flutter):"
if curl -s http://localhost:3000 > /dev/null; then
    echo "   ✅ Frontend is running at http://localhost:3000"
    echo "   🌐 User App: http://localhost:3000"
else
    echo "   ❌ Frontend server is not responding"
    echo "   💡 Run: ./start-frontend.sh"
fi

echo ""

# Check API connectivity
echo "🔌 API Connectivity:"
if curl -s http://localhost:3000 > /dev/null && curl -s http://localhost:8000/api/ > /dev/null; then
    echo "   ✅ Frontend can communicate with Backend API"
else
    echo "   ⚠️  API connectivity may have issues"
fi

echo ""

# Show running processes
echo "🔄 Running Processes:"
if pgrep -f "php.*localhost:8000" > /dev/null; then
    echo "   ✅ PHP server process is running"
else
    echo "   ❌ PHP server process not found"
fi

if pgrep -f "flutter.*web-server" > /dev/null; then
    echo "   ✅ Flutter web server process is running"
else
    echo "   ❌ Flutter web server process not found"
fi

echo ""
echo "=================================================="
echo "🎉 Status check complete!"
echo ""
echo "📖 Next Steps:"
echo "   1. Visit http://localhost:8000/setup.php to configure the database"
echo "   2. Create an admin account"
echo "   3. Access the admin panel to add users and courses"
echo "   4. Test the user app at http://localhost:3000"
echo ""
echo "📚 For detailed setup instructions, see LOCAL_SETUP.md"

t=2025-07-17T10:14:09+0530 lvl=info msg="no configuration paths supplied"
t=2025-07-17T10:14:09+0530 lvl=info msg="using configuration at default config path" path="/Users/<USER>/Library/Application Support/ngrok/ngrok.yml"
t=2025-07-17T10:14:09+0530 lvl=info msg="open config file" path="/Users/<USER>/Library/Application Support/ngrok/ngrok.yml" err=nil
t=2025-07-17T10:14:09+0530 lvl=info msg="starting web service" obj=web addr=127.0.0.1:4040 allow_hosts=[]
t=2025-07-17T10:14:09+0530 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-17T10:14:09+0530 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-17T10:14:10+0530 lvl=info msg="started tunnel" obj=tunnels name=command_line addr=http://localhost:8080 url=https://4b27c1e54d36.ngrok-free.app
t=2025-07-17T10:18:27+0530 lvl=info msg=start pg=/api/tunnels id=425a5bbca1735067
t=2025-07-17T10:18:27+0530 lvl=info msg=end pg=/api/tunnels id=425a5bbca1735067 status=200 dur=347.833µs
t=2025-07-17T10:41:19+0530 lvl=info msg=start pg=/api/tunnels id=16468cb7aef6cfdc
t=2025-07-17T10:41:19+0530 lvl=info msg=end pg=/api/tunnels id=16468cb7aef6cfdc status=200 dur=4.706292ms
t=2025-07-17T10:58:51+0530 lvl=warn msg="failed to open private leg" id=42980fd53b2b privaddr=localhost:8080 err="dial tcp [::1]:8080: connect: connection refused"
t=2025-07-17T10:58:52+0530 lvl=warn msg="failed to open private leg" id=d89823fc7bb8 privaddr=localhost:8080 err="dial tcp [::1]:8080: connect: connection refused"
t=2025-07-17T10:59:55+0530 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=f6c1c7146002 clientid=553e60ebaf1123894d828342f7888449
t=2025-07-17T10:59:55+0530 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=f2362c98498a err="session closed"
t=2025-07-17T11:00:05+0530 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-17T11:00:15+0530 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-17T11:00:26+0530 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to dial ngrok server with address \"connect.ngrok-agent.com:443\": dial tcp: lookup connect.ngrok-agent.com: i/o timeout"
t=2025-07-17T11:00:33+0530 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-17T11:00:33+0530 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-07-17T11:01:48+0530 lvl=eror msg="heartbeat timeout, terminating session" obj=tunnels.session obj=csess id=79fc40cd5190 clientid=553e60ebaf1123894d828342f7888449
t=2025-07-17T11:01:48+0530 lvl=eror msg="session closed, starting reconnect loop" obj=tunnels.session obj=csess id=f2362c98498a err="session closed"
t=2025-07-17T11:01:58+0530 lvl=eror msg="failed to reconnect session" obj=tunnels.session err="failed to send authentication request: failed to fetch CRL. errors encountered: timed out fetching CRL"
t=2025-07-17T11:01:59+0530 lvl=info msg="client session established" obj=tunnels.session
t=2025-07-17T11:01:59+0530 lvl=info msg="tunnel session started" obj=tunnels.session

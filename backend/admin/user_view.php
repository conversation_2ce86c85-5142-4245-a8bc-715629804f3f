<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/Auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/includes/navigation.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Initialize navigation
$navigation = new AdminNavigation('user_view.php', $_SESSION['admin_name'] ?? 'Admin');

// Get user ID from URL
$userId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($userId === 0) {
    header('Location: users_modern.php');
    exit;
}

$database = new Database();
$db = $database->getConnection();
$auth = new Auth();

// Fetch user details with course information
$stmt = $db->prepare("
    SELECT u.*,
           c.title as course_title,
           c.description as course_description,
           c.duration_days,
           c.video_unlock_interval,
           uc.assigned_at,
           uc.started_at,
           uc.completed_at
    FROM users u
    LEFT JOIN user_courses uc ON u.id = uc.user_id AND uc.is_active = 1
    LEFT JOIN courses c ON uc.course_id = c.id
    WHERE u.id = ?
");
$stmt->execute([$userId]);
$user = $stmt->fetch();

if (!$user) {
    header('Location: users.php');
    exit;
}

// Fetch user's video progress
$stmt = $db->prepare("
    SELECT vp.*, cv.title as video_title, cv.vimeo_video_id, cv.unlock_day, cv.duration_seconds
    FROM user_video_progress vp
    JOIN course_videos cv ON vp.video_id = cv.id
    WHERE vp.user_id = ?
    ORDER BY cv.unlock_day ASC
");
$stmt->execute([$userId]);
$videoProgress = $stmt->fetchAll();

// Fetch user sessions
$stmt = $db->prepare("
    SELECT * FROM user_sessions
    WHERE user_id = ?
    ORDER BY started_at DESC
    LIMIT 10
");
$stmt->execute([$userId]);
$sessions = $stmt->fetchAll();

// Calculate BMI
function calculateBMI($weight, $height) {
    if ($weight && $height && $height > 0) {
        $heightInMeters = $height / 100;
        return $weight / ($heightInMeters * $heightInMeters);
    }
    return null;
}

function getBMICategory($bmi) {
    if ($bmi === null) return 'Unknown';
    if ($bmi < 18.5) return 'Underweight';
    if ($bmi < 25) return 'Normal';
    if ($bmi < 30) return 'Overweight';
    return 'Obese';
}

function getBMIColor($bmi) {
    if ($bmi === null) return '#6c757d';
    if ($bmi < 18.5) return '#0dcaf0';
    if ($bmi < 25) return '#198754';
    if ($bmi < 30) return '#fd7e14';
    return '#dc3545';
}

$bmi = calculateBMI($user['weight'], $user['height']);
$bmiCategory = getBMICategory($bmi);
$bmiColor = getBMIColor($bmi);

// Calculate progress statistics
$totalVideos = count($videoProgress);
$completedVideos = array_filter($videoProgress, function($vp) {
    return $vp['is_completed'] == 1;
});
$completedCount = count($completedVideos);
$progressPercentage = $totalVideos > 0 ? ($completedCount / $totalVideos) * 100 : 0;

// Calculate current week
$currentWeek = 1;
if ($user['created_at']) {
    $startDate = new DateTime($user['created_at']);
    $now = new DateTime();
    $daysSinceStart = $now->diff($startDate)->days;
    $currentWeek = floor($daysSinceStart / ($user['video_unlock_interval'] ?: 8)) + 1;
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_profile':
                $fullName = sanitizeInput($_POST['full_name']);
                $email = sanitizeInput($_POST['email']);
                $phone = sanitizeInput($_POST['phone']);
                $age = (int)$_POST['age'];
                $weight = (float)$_POST['weight'];
                $height = (float)$_POST['height'];
                $targetWeight = (float)$_POST['target_weight'];
                
                $stmt = $db->prepare("
                    UPDATE users SET 
                        full_name = ?, email = ?, phone = ?, age = ?, 
                        weight = ?, height = ?, target_weight = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                
                if ($stmt->execute([$fullName, $email, $phone, $age, $weight, $height, $targetWeight, $userId])) {
                    $message = 'Profile updated successfully!';
                    $messageType = 'success';
                    // Refresh user data
                    $stmt = $db->prepare("SELECT u.*, c.title as course_title FROM users u LEFT JOIN courses c ON u.course_id = c.id WHERE u.id = ?");
                    $stmt->execute([$userId]);
                    $user = $stmt->fetch();
                    $bmi = calculateBMI($user['weight'], $user['height']);
                    $bmiCategory = getBMICategory($bmi);
                    $bmiColor = getBMIColor($bmi);
                } else {
                    $message = 'Error updating profile!';
                    $messageType = 'danger';
                }
                break;
                
            case 'toggle_status':
                $newStatus = $user['is_active'] ? 0 : 1;
                $stmt = $db->prepare("UPDATE users SET is_active = ?, updated_at = NOW() WHERE id = ?");
                if ($stmt->execute([$newStatus, $userId])) {
                    $user['is_active'] = $newStatus;
                    $message = $newStatus ? 'User activated successfully!' : 'User deactivated successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Error updating user status!';
                    $messageType = 'danger';
                }
                break;
                
            case 'generate_auth_link':
                $authLink = $auth->generateAuthLink($userId, 1);
                if ($authLink) {
                    $message = 'Authentication link generated successfully!';
                    $messageType = 'success';
                    $generatedAuthLink = $authLink;
                } else {
                    $message = 'Error generating authentication link!';
                    $messageType = 'danger';
                }
                break;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Profile - <?= htmlspecialchars($user['full_name']) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <?= $navigation->getNavigationCSS() ?>
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #06b6d4;
            --light: #f8fafc;
            --dark: #1e293b;
            --border: #e2e8f0;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            --gradient-surface: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        }

        * {
            box-sizing: border-box;
        }

        body {
            background: var(--surface);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            line-height: 1.6;
            color: var(--text-primary);
            font-size: 14px;
        }

        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        .main-container {
            background: var(--light);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
            overflow: hidden;
            margin-bottom: var(--spacing-xl);
        }

        .header-section {
            background: var(--light);
            border-bottom: 1px solid var(--border);
            padding: var(--spacing-2xl) var(--spacing-xl);
            position: relative;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            opacity: 0.6;
        }

        .dashboard-title {
            font-size: 2rem;
            font-weight: 700;
            margin: 0 0 var(--spacing-xs) 0;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .dashboard-title i {
            color: var(--primary);
            font-size: 1.75rem;
        }

        .dashboard-subtitle {
            font-size: 1rem;
            margin: 0;
            font-weight: 400;
            color: var(--text-secondary);
        }
        
        .profile-avatar {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1rem;
            border: 4px solid rgba(255, 255, 255, 0.3);
        }
        
        .stat-card {
            background: var(--gradient-surface);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-lg);
        }

        .stat-card:hover::before {
            transform: scaleX(1);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1rem;
            background: var(--gradient-primary);
            color: white;
            box-shadow: 0 8px 16px rgba(99, 102, 241, 0.3);
        }
        
        .progress-ring {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            background: conic-gradient(var(--primary) <?= $progressPercentage * 3.6 ?>deg, #e5e7eb 0deg);
        }

        .progress-ring::before {
            content: '';
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: white;
            position: absolute;
        }

        .progress-text {
            position: relative;
            z-index: 1;
            font-weight: bold;
            color: var(--primary);
        }

        .section-card {
            background: white;
            border-radius: 20px;
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
            overflow: hidden;
        }
        
        .section-header {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            padding: 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .btn-premium {
            background: var(--gradient-primary);
            border: none;
            border-radius: 12px;
            padding: 0.875rem 1.5rem;
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-premium:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(99, 102, 241, 0.4);
            color: white;
            text-decoration: none;
        }
        
        .video-progress-item {
            padding: 1rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            transition: background-color 0.3s ease;
        }
        
        .video-progress-item:hover {
            background-color: rgba(99, 102, 241, 0.05);
        }
        
        .video-progress-item:last-child {
            border-bottom: none;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .badge-active {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }
        
        .badge-inactive {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }
        
        .form-control {
            border: 2px solid var(--border);
            border-radius: 12px;
            padding: 0.875rem 1rem;
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .nav-tabs {
            border-bottom: 2px solid var(--border);
        }

        .nav-tabs .nav-link {
            border: none;
            border-radius: 12px 12px 0 0;
            margin-right: 0.5rem;
            color: #6b7280;
            font-weight: 600;
            padding: 1rem 1.5rem;
            transition: all 0.2s ease;
        }

        .nav-tabs .nav-link.active {
            background: var(--primary);
            color: white;
            box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);
        }

        .nav-tabs .nav-link:hover:not(.active) {
            background: rgba(99, 102, 241, 0.1);
            color: var(--primary);
        }
        
        .table {
            margin-bottom: 0;
            width: 100%;
        }

        .table th {
            background: var(--gradient-surface);
            border: none;
            font-weight: 600;
            color: #475569;
            padding: 1.25rem 1rem;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .table td {
            padding: 1.25rem 1rem;
            border-color: #f1f5f9;
            vertical-align: middle;
            border-bottom: 1px solid var(--border);
        }

        .table tbody tr {
            transition: all 0.2s ease;
        }

        .table tbody tr:hover {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.02), rgba(139, 92, 246, 0.02));
            transform: scale(1.001);
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }
        
        .back-btn {
            background: var(--light);
            border: 1px solid var(--border);
            color: var(--text-primary);
            border-radius: var(--radius);
            padding: var(--spacing-sm) var(--spacing-md);
            text-decoration: none;
            transition: all 0.2s ease;
            font-weight: 500;
            font-size: 0.875rem;
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .back-btn:hover {
            background: var(--surface);
            color: var(--primary);
            text-decoration: none;
            box-shadow: var(--shadow-sm);
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn-header {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .btn-header:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <?= $navigation->renderNavigation() ?>

    <div class="main-content">
        <div class="dashboard-container">
            <div class="main-container">
            <!-- Header Section -->
            <div class="header-section">
                <div class="d-flex justify-content-between align-items-start mb-4">
                    <div class="d-flex gap-2 align-items-center">
                        <button class="mobile-menu-btn" type="button">
                            <i class="bi bi-list"></i>
                        </button>
                        <a href="users_modern.php" class="back-btn">
                            <i class="bi bi-arrow-left me-2"></i>Back to Users
                        </a>
                        <a href="dashboard_weightloss.php" class="back-btn">
                            <i class="bi bi-heart-pulse me-2"></i>Dashboard
                        </a>
                    </div>
                    <div class="header-actions">
                        <form method="POST" class="d-inline">
                            <input type="hidden" name="action" value="toggle_status">
                            <button type="submit" class="btn-header">
                                <i class="bi bi-<?= $user['is_active'] ? 'pause' : 'play' ?>-fill me-1"></i>
                                <?= $user['is_active'] ? 'Deactivate' : 'Activate' ?>
                            </button>
                        </form>
                        <form method="POST" class="d-inline">
                            <input type="hidden" name="action" value="generate_auth_link">
                            <button type="submit" class="btn-header">
                                <i class="bi bi-link-45deg me-1"></i>Generate Link
                            </button>
                        </form>
                    </div>
                </div>
                
                <div class="row align-items-center">
                    <div class="col-md-2">
                        <div class="profile-avatar mx-auto">
                            <?= strtoupper(substr($user['full_name'], 0, 2)) ?>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h1 class="dashboard-title mb-2">
                            <i class="bi bi-person-circle"></i><?= htmlspecialchars($user['full_name']) ?>
                        </h1>
                        <p class="dashboard-subtitle mb-3">User Profile & Progress Overview</p>
                        <p class="mb-1 opacity-75">
                            <i class="bi bi-envelope me-2"></i><?= htmlspecialchars($user['email']) ?>
                        </p>
                        <?php if ($user['phone']): ?>
                        <p class="mb-1 opacity-75">
                            <i class="bi bi-telephone me-2"></i><?= htmlspecialchars($user['phone']) ?>
                        </p>
                        <?php endif; ?>
                        <p class="mb-0 opacity-75">
                            <i class="bi bi-calendar me-2"></i>Member since <?= date('M d, Y', strtotime($user['created_at'])) ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="d-flex justify-content-end align-items-center gap-3">
                            <div class="progress-ring">
                                <div class="progress-text"><?= number_format($progressPercentage, 0) ?>%</div>
                            </div>
                            <div>
                                <div class="status-badge <?= $user['is_active'] ? 'badge-active' : 'badge-inactive' ?>">
                                    <?= $user['is_active'] ? 'Active' : 'Inactive' ?>
                                </div>
                                <div class="mt-2 text-white-50">
                                    <small>Last login: <?= $user['last_login'] ? date('M d, Y H:i', strtotime($user['last_login'])) : 'Never' ?></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alert Messages -->
            <?php if ($message): ?>
            <div class="container mt-3">
                <div class="alert alert-<?= $messageType ?> alert-dismissible fade show" role="alert">
                    <?= $message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
            <?php endif; ?>

            <!-- Generated Auth Link -->
            <?php if (isset($generatedAuthLink)): ?>
            <div class="container mt-3">
                <div class="alert alert-info" role="alert">
                    <h6><i class="bi bi-link-45deg me-2"></i>Generated Authentication Link:</h6>
                    <div class="input-group mt-2">
                        <input type="text" class="form-control" id="authLink" value="<?= htmlspecialchars($generatedAuthLink) ?>" readonly>
                        <button class="btn btn-outline-primary" type="button" onclick="copyToClipboard('authLink')">
                            <i class="bi bi-clipboard"></i> Copy
                        </button>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Main Content -->
        <div class="container mt-4">
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="stat-card text-center">
                        <div class="stat-icon mx-auto" style="background: rgba(99, 102, 241, 0.1); color: var(--primary-color);">
                            <i class="bi bi-calendar-week"></i>
                        </div>
                        <h3 class="mb-1" style="color: var(--primary-color);">Week <?= $currentWeek ?></h3>
                        <p class="text-muted mb-0">Current Week</p>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card text-center">
                        <div class="stat-icon mx-auto" style="background: rgba(16, 185, 129, 0.1); color: var(--success-color);">
                            <i class="bi bi-play-circle-fill"></i>
                        </div>
                        <h3 class="mb-1" style="color: var(--success-color);"><?= $completedCount ?>/<?= $totalVideos ?></h3>
                        <p class="text-muted mb-0">Videos Completed</p>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card text-center">
                        <div class="stat-icon mx-auto" style="background: rgba(245, 158, 11, 0.1); color: var(--warning-color);">
                            <i class="bi bi-speedometer2"></i>
                        </div>
                        <h3 class="mb-1" style="color: <?= $bmiColor ?>;">
                            <?= $bmi ? number_format($bmi, 1) : '--' ?>
                        </h3>
                        <p class="text-muted mb-0">BMI (<?= $bmiCategory ?>)</p>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card text-center">
                        <div class="stat-icon mx-auto" style="background: rgba(6, 182, 212, 0.1); color: var(--info-color);">
                            <i class="bi bi-trophy"></i>
                        </div>
                        <h3 class="mb-1" style="color: var(--info-color);"><?= number_format($progressPercentage, 0) ?>%</h3>
                        <p class="text-muted mb-0">Progress</p>
                    </div>
                </div>
            </div>

            <!-- Tabbed Content -->
            <div class="section-card">
                <ul class="nav nav-tabs" id="userTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button" role="tab">
                            <i class="bi bi-person me-2"></i>Profile Information
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="progress-tab" data-bs-toggle="tab" data-bs-target="#progress" type="button" role="tab">
                            <i class="bi bi-graph-up me-2"></i>Video Progress
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="sessions-tab" data-bs-toggle="tab" data-bs-target="#sessions" type="button" role="tab">
                            <i class="bi bi-clock-history me-2"></i>Login Sessions
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="course-tab" data-bs-toggle="tab" data-bs-target="#course" type="button" role="tab">
                            <i class="bi bi-book me-2"></i>Course Details
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="userTabsContent">
                    <!-- Profile Information Tab -->
                    <div class="tab-pane fade show active" id="profile" role="tabpanel">
                        <div class="p-4">
                            <form method="POST">
                                <input type="hidden" name="action" value="update_profile">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="full_name" class="form-label fw-bold">Full Name</label>
                                        <input type="text" class="form-control" id="full_name" name="full_name"
                                               value="<?= htmlspecialchars($user['full_name']) ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label fw-bold">Email</label>
                                        <input type="email" class="form-control" id="email" name="email"
                                               value="<?= htmlspecialchars($user['email']) ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label fw-bold">Phone</label>
                                        <input type="text" class="form-control" id="phone" name="phone"
                                               value="<?= htmlspecialchars($user['phone'] ?? '') ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="age" class="form-label fw-bold">Age</label>
                                        <input type="number" class="form-control" id="age" name="age"
                                               value="<?= $user['age'] ?>" min="1" max="120">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="height" class="form-label fw-bold">Height (cm)</label>
                                        <input type="number" class="form-control" id="height" name="height"
                                               value="<?= $user['height'] ?>" step="0.1" min="1">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="weight" class="form-label fw-bold">Weight (kg)</label>
                                        <input type="number" class="form-control" id="weight" name="weight"
                                               value="<?= $user['weight'] ?>" step="0.1" min="1">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="target_weight" class="form-label fw-bold">Target Weight (kg)</label>
                                        <input type="number" class="form-control" id="target_weight" name="target_weight"
                                               value="<?= $user['target_weight'] ?>" step="0.1" min="1">
                                    </div>
                                </div>

                                <!-- BMI Information -->
                                <?php if ($bmi): ?>
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <div class="alert" style="background: rgba(<?= $bmiColor === '#198754' ? '25, 135, 84' : ($bmiColor === '#fd7e14' ? '253, 126, 20' : ($bmiColor === '#dc3545' ? '220, 53, 69' : '108, 117, 125')) ?>, 0.1); border: 1px solid <?= $bmiColor ?>; color: <?= $bmiColor ?>;">
                                            <h6><i class="bi bi-info-circle me-2"></i>BMI Information</h6>
                                            <p class="mb-0">
                                                Current BMI: <strong><?= number_format($bmi, 1) ?></strong> -
                                                Category: <strong><?= $bmiCategory ?></strong>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <div class="text-end">
                                    <button type="submit" class="btn btn-premium">
                                        <i class="bi bi-save me-2"></i>Update Profile
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Video Progress Tab -->
                    <div class="tab-pane fade" id="progress" role="tabpanel">
                        <div class="p-4">
                            <?php if (empty($videoProgress)): ?>
                            <div class="text-center py-5">
                                <i class="bi bi-play-circle display-1 text-muted"></i>
                                <h4 class="mt-3 text-muted">No Video Progress</h4>
                                <p class="text-muted">This user hasn't started watching any videos yet.</p>
                            </div>
                            <?php else: ?>
                            <div class="mb-3">
                                <h5>Video Progress Overview</h5>
                                <div class="progress mb-3" style="height: 10px;">
                                    <div class="progress-bar" role="progressbar"
                                         style="width: <?= $progressPercentage ?>%; background: var(--primary-color);">
                                    </div>
                                </div>
                                <p class="text-muted">
                                    <?= $completedCount ?> of <?= $totalVideos ?> videos completed
                                    (<?= number_format($progressPercentage, 1) ?>%)
                                </p>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Video</th>
                                            <th>Unlock Day</th>
                                            <th>Duration</th>
                                            <th>Progress</th>
                                            <th>Status</th>
                                            <th>Last Watched</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($videoProgress as $vp): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-play-circle-fill me-2 text-primary"></i>
                                                    <strong><?= htmlspecialchars($vp['video_title']) ?></strong>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">Day <?= $vp['unlock_day'] ?></span>
                                            </td>
                                            <td>
                                                <?= gmdate("i:s", $vp['duration_seconds']) ?>
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 6px; width: 80px;">
                                                    <div class="progress-bar" style="width: <?= $vp['progress_percentage'] ?>%; background: var(--success);"></div>
                                                </div>
                                                <small class="text-muted"><?= number_format($vp['progress_percentage'], 1) ?>%</small>
                                            </td>
                                            <td>
                                                <?php if ($vp['is_completed']): ?>
                                                    <span class="badge bg-success">Completed</span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">In Progress</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('M d, Y H:i', strtotime($vp['last_watched_at'])) ?>
                                                </small>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Login Sessions Tab -->
                    <div class="tab-pane fade" id="sessions" role="tabpanel">
                        <div class="p-4">
                            <?php if (empty($sessions)): ?>
                            <div class="text-center py-5">
                                <i class="bi bi-clock-history display-1 text-muted"></i>
                                <h4 class="mt-3 text-muted">No Login Sessions</h4>
                                <p class="text-muted">This user hasn't logged in yet.</p>
                            </div>
                            <?php else: ?>
                            <h5 class="mb-3">Recent Login Sessions</h5>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Session Token</th>
                                            <th>Device Info</th>
                                            <th>IP Address</th>
                                            <th>Created At</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($sessions as $session): ?>
                                        <tr>
                                            <td>
                                                <code class="text-muted"><?= substr($session['session_token'], 0, 20) ?>...</code>
                                            </td>
                                            <td>
                                                <?php
                                                $deviceInfo = json_decode($session['device_info'], true);
                                                if ($deviceInfo && isset($deviceInfo['platform'])) {
                                                    echo '<span class="badge bg-info">' . htmlspecialchars($deviceInfo['platform']) . '</span>';
                                                } else {
                                                    echo '<span class="text-muted">Unknown</span>';
                                                }
                                                ?>
                                            </td>
                                            <td><?= htmlspecialchars($session['ip_address']) ?></td>
                                            <td><?= date('M d, Y H:i:s', strtotime($session['started_at'])) ?></td>
                                            <td>
                                                <span class="badge bg-success">Active</span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Course Details Tab -->
                    <div class="tab-pane fade" id="course" role="tabpanel">
                        <div class="p-4">
                            <?php if ($user['course_title']): ?>
                            <div class="row">
                                <div class="col-md-8">
                                    <h5><?= htmlspecialchars($user['course_title']) ?></h5>
                                    <p class="text-muted"><?= htmlspecialchars($user['course_description']) ?></p>

                                    <div class="row mt-4">
                                        <div class="col-md-6">
                                            <div class="stat-card">
                                                <div class="d-flex align-items-center">
                                                    <div class="stat-icon me-3" style="background: rgba(99, 102, 241, 0.1); color: var(--primary-color);">
                                                        <i class="bi bi-calendar-range"></i>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">Duration</h6>
                                                        <p class="text-muted mb-0"><?= $user['duration_days'] ?> days</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="stat-card">
                                                <div class="d-flex align-items-center">
                                                    <div class="stat-icon me-3" style="background: rgba(16, 185, 129, 0.1); color: var(--success-color);">
                                                        <i class="bi bi-unlock"></i>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">Unlock Interval</h6>
                                                        <p class="text-muted mb-0">Every <?= $user['video_unlock_interval'] ?> days</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="progress-ring mb-3">
                                            <div class="progress-text"><?= number_format($progressPercentage, 0) ?>%</div>
                                        </div>
                                        <h6>Course Progress</h6>
                                        <p class="text-muted">Week <?= $currentWeek ?> of 4</p>
                                    </div>
                                </div>
                            </div>
                            <?php else: ?>
                            <div class="text-center py-5">
                                <i class="bi bi-book display-1 text-muted"></i>
                                <h4 class="mt-3 text-muted">No Course Assigned</h4>
                                <p class="text-muted">This user hasn't been assigned to any course yet.</p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <?= $navigation->getNavigationJS() ?>
    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            element.select();
            element.setSelectionRange(0, 99999);

            try {
                document.execCommand('copy');
                showToast('Copied to clipboard!', 'success');
            } catch (err) {
                navigator.clipboard.writeText(element.value).then(function() {
                    showToast('Copied to clipboard!', 'success');
                }).catch(function(err) {
                    showToast('Failed to copy to clipboard', 'error');
                });
            }
        }

        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'error' ? 'danger' : 'success'} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 250px;';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            document.body.appendChild(toast);

            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>

<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/Auth.php';
require_once __DIR__ . '/includes/navigation.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Initialize navigation
$navigation = new AdminNavigation('analytics.php', $_SESSION['admin_name'] ?? 'Admin');

$auth = new Auth();
$database = new Database();
$db = $database->getConnection();

// Get basic analytics data
$stmt = $db->query("SELECT COUNT(*) as total FROM users WHERE is_active = 1");
$totalUsers = $stmt->fetch()['total'];

$stmt = $db->query("SELECT COUNT(*) as total FROM user_sessions WHERE DATE(started_at) >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
$weeklyActiveSessions = $stmt->fetch()['total'];

$stmt = $db->query("SELECT COUNT(*) as total FROM user_video_progress WHERE is_completed = 1 AND DATE(last_watched_at) >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
$weeklyCompletedVideos = $stmt->fetch()['total'];

// Get recent activity
$stmt = $db->query("
    SELECT u.full_name, u.email, u.created_at as activity_time, 'new_user' as activity_type
    FROM users u 
    WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    
    UNION ALL
    
    SELECT u.full_name, u.email, vp.last_watched_at as activity_time, 'video_completed' as activity_type
    FROM user_video_progress vp
    JOIN users u ON vp.user_id = u.id
    WHERE vp.is_completed = 1 AND vp.last_watched_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    
    ORDER BY activity_time DESC
    LIMIT 10
");
$recent_activity = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics - HomeWorkout Pro Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <?= $navigation->getNavigationCSS() ?>
    <style>
        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        .main-card {
            background: var(--light);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
            overflow: hidden;
            margin-bottom: var(--spacing-xl);
        }

        .header-section {
            background: var(--light);
            border-bottom: 1px solid var(--border);
            padding: var(--spacing-2xl) var(--spacing-xl);
            position: relative;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dashboard-title {
            font-size: 2rem;
            font-weight: 700;
            margin: 0 0 var(--spacing-xs) 0;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .dashboard-title i {
            color: var(--primary);
            font-size: 1.75rem;
        }

        .dashboard-subtitle {
            font-size: 1rem;
            margin: 0;
            font-weight: 400;
            color: var(--text-secondary);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-lg);
            padding: var(--spacing-xl);
            background: var(--surface);
        }

        .stat-card {
            background: var(--light);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border);
            transition: all 0.2s ease;
            text-align: center;
        }

        .stat-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            margin: 0 auto var(--spacing-md) auto;
            color: white;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin: 0 0 var(--spacing-xs) 0;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin: 0;
            font-weight: 500;
        }

        .content-section {
            padding: var(--spacing-xl);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 var(--spacing-lg) 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .section-title i {
            color: var(--primary);
            font-size: 1.125rem;
        }

        .table-container {
            background: var(--light);
            border-radius: var(--radius-md);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border);
        }

        .coming-soon {
            text-align: center;
            padding: var(--spacing-2xl);
            color: var(--text-secondary);
        }

        .coming-soon i {
            font-size: 4rem;
            color: var(--text-muted);
            margin-bottom: var(--spacing-lg);
        }

        .coming-soon h3 {
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
        }

        @media (max-width: 768px) {
            .dashboard-container {
                padding: var(--spacing-md);
            }
            
            .header-section {
                padding: var(--spacing-xl) var(--spacing-md);
            }
            
            .header-content {
                flex-direction: column;
                gap: var(--spacing-lg);
                text-align: center;
            }
            
            .dashboard-title {
                font-size: 1.75rem;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                padding: var(--spacing-md);
                gap: var(--spacing-md);
            }
            
            .content-section {
                padding: var(--spacing-md);
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <?= $navigation->renderNavigation() ?>
    
    <div class="main-content">
        <div class="dashboard-container">
            <div class="main-card">
                <!-- Header Section -->
                <div class="header-section">
                    <div class="header-content">
                        <div>
                            <button class="mobile-menu-btn" type="button">
                                <i class="bi bi-list"></i>
                            </button>
                            <h1 class="dashboard-title">
                                <i class="bi bi-graph-up"></i>Analytics
                            </h1>
                            <p class="dashboard-subtitle">Detailed insights and reports</p>
                        </div>
                    </div>
                </div>

                <!-- Statistics Grid -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--primary);">
                            <i class="bi bi-people-fill"></i>
                        </div>
                        <div class="stat-value"><?= number_format($totalUsers) ?></div>
                        <div class="stat-label">Total Active Users</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--secondary);">
                            <i class="bi bi-activity"></i>
                        </div>
                        <div class="stat-value"><?= number_format($weeklyActiveSessions) ?></div>
                        <div class="stat-label">Weekly Sessions</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--success);">
                            <i class="bi bi-play-circle-fill"></i>
                        </div>
                        <div class="stat-value"><?= number_format($weeklyCompletedVideos) ?></div>
                        <div class="stat-label">Videos Completed</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--info);">
                            <i class="bi bi-clock-history"></i>
                        </div>
                        <div class="stat-value"><?= count($recent_activity) ?></div>
                        <div class="stat-label">Recent Activities</div>
                    </div>
                </div>

                <!-- Content Section -->
                <div class="content-section">
                    <h2 class="section-title">
                        <i class="bi bi-clock-history"></i>
                        Recent Activity
                    </h2>
                    
                    <div class="table-container">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Activity</th>
                                        <th>User</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($recent_activity)): ?>
                                    <tr>
                                        <td colspan="3" class="text-center py-4">
                                            <i class="bi bi-activity text-muted" style="font-size: 2rem;"></i>
                                            <p class="text-muted mt-2 mb-0">No recent activity</p>
                                        </td>
                                    </tr>
                                    <?php else: ?>
                                    <?php foreach ($recent_activity as $activity): ?>
                                    <tr>
                                        <td>
                                            <i class="bi bi-<?= $activity['activity_type'] === 'new_user' ? 'person-plus' : 'play-circle' ?> me-2 text-primary"></i>
                                            <?= $activity['activity_type'] === 'new_user' ? 'New User Registration' : 'Video Completed' ?>
                                        </td>
                                        <td><?= htmlspecialchars($activity['full_name']) ?></td>
                                        <td><?= date('M d, H:i', strtotime($activity['activity_time'])) ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="coming-soon">
                        <i class="bi bi-graph-up-arrow"></i>
                        <h3>Advanced Analytics Coming Soon</h3>
                        <p>Detailed charts, reports, and insights will be available here.</p>
                        <p class="text-muted">Features in development:</p>
                        <ul class="list-unstyled text-muted">
                            <li>• User engagement trends</li>
                            <li>• Video completion rates</li>
                            <li>• Weight loss progress tracking</li>
                            <li>• Custom date range reports</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <?= $navigation->getNavigationJS() ?>
</body>
</html>

<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/Auth.php';
require_once __DIR__ . '/includes/navigation.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Initialize navigation
$navigation = new AdminNavigation('settings.php', $_SESSION['admin_name'] ?? 'Admin');

$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'update_profile':
            $username = sanitizeInput($_POST['username']);
            $email = sanitizeInput($_POST['email']);
            $currentPassword = $_POST['current_password'] ?? '';
            $newPassword = $_POST['new_password'] ?? '';
            
            if (!empty($username) && !empty($email)) {
                try {
                    $database = new Database();
                    $db = $database->getConnection();
                    
                    // Verify current password if new password is provided
                    if (!empty($newPassword)) {
                        $stmt = $db->prepare("SELECT password_hash FROM admins WHERE id = ?");
                        $stmt->execute([$_SESSION['admin_id']]);
                        $admin = $stmt->fetch();
                        
                        if (!verifyPassword($currentPassword, $admin['password_hash'])) {
                            throw new Exception('Current password is incorrect');
                        }
                        
                        // Update with new password
                        $passwordHash = hashPassword($newPassword);
                        $stmt = $db->prepare("UPDATE admins SET username = ?, email = ?, password_hash = ? WHERE id = ?");
                        $stmt->execute([$username, $email, $passwordHash, $_SESSION['admin_id']]);
                    } else {
                        // Update without password change
                        $stmt = $db->prepare("UPDATE admins SET username = ?, email = ? WHERE id = ?");
                        $stmt->execute([$username, $email, $_SESSION['admin_id']]);
                    }
                    
                    $_SESSION['admin_username'] = $username;
                    $_SESSION['admin_email'] = $email;
                    
                    $message = 'Profile updated successfully!';
                    $messageType = 'success';
                    
                } catch (Exception $e) {
                    $message = 'Error updating profile: ' . $e->getMessage();
                    $messageType = 'danger';
                }
            } else {
                $message = 'Username and email are required';
                $messageType = 'danger';
            }
            break;
    }
}

// Get current admin info
$database = new Database();
$db = $database->getConnection();
$stmt = $db->prepare("SELECT * FROM admins WHERE id = ?");
$stmt->execute([$_SESSION['admin_id']]);
$admin = $stmt->fetch();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - HomeWorkout Pro Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <?= $navigation->getNavigationCSS() ?>
    <style>
        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        .main-card {
            background: var(--light);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
            overflow: hidden;
            margin-bottom: var(--spacing-xl);
        }

        .header-section {
            background: var(--light);
            border-bottom: 1px solid var(--border);
            padding: var(--spacing-2xl) var(--spacing-xl);
            position: relative;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dashboard-title {
            font-size: 2rem;
            font-weight: 700;
            margin: 0 0 var(--spacing-xs) 0;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .dashboard-title i {
            color: var(--primary);
            font-size: 1.75rem;
        }

        .dashboard-subtitle {
            font-size: 1rem;
            margin: 0;
            font-weight: 400;
            color: var(--text-secondary);
        }

        .content-section {
            padding: var(--spacing-xl);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 var(--spacing-lg) 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .section-title i {
            color: var(--primary);
            font-size: 1.125rem;
        }

        .form-container {
            background: var(--light);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border);
        }

        .btn-minimal {
            background: var(--light);
            color: var(--text-primary);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: var(--spacing-sm) var(--spacing-md);
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .btn-minimal:hover {
            background: var(--surface);
            color: var(--primary);
            text-decoration: none;
            box-shadow: var(--shadow-sm);
        }

        .btn-primary {
            background: var(--primary);
            color: white;
            border: 1px solid var(--primary);
        }

        .btn-primary:hover {
            background: var(--primary-light);
            border-color: var(--primary-light);
            color: white;
        }

        @media (max-width: 768px) {
            .dashboard-container {
                padding: var(--spacing-md);
            }

            .header-section {
                padding: var(--spacing-xl) var(--spacing-md);
            }

            .content-section {
                padding: var(--spacing-md);
            }
        }
    </style>
</head>
<body>
    <?= $navigation->renderNavigation() ?>

    <div class="main-content">
        <div class="dashboard-container">
            <div class="main-card">
                <!-- Header Section -->
                <div class="header-section">
                    <div class="header-content">
                        <div>
                            <button class="mobile-menu-btn" type="button">
                                <i class="bi bi-list"></i>
                            </button>
                            <h1 class="dashboard-title">
                                <i class="bi bi-gear-fill"></i>Settings
                            </h1>
                            <p class="dashboard-subtitle">System configuration and preferences</p>
                        </div>
                    </div>
                </div>

                <!-- Content Section -->
                <div class="content-section">
                    <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php endif; ?>

                    <div class="row">
                        <div class="col-lg-8">
                            <h2 class="section-title">
                                <i class="bi bi-person-gear"></i>
                                Profile Settings
                            </h2>

                            <!-- Profile Settings -->
                            <div class="form-container">
                                <form method="POST" action="">
                                    <input type="hidden" name="action" value="update_profile">
                                <form method="POST" action="">
                                    <input type="hidden" name="action" value="update_profile">
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="username" class="form-label">Username</label>
                                                <input type="text" class="form-control" id="username" name="username" 
                                                       value="<?php echo htmlspecialchars($admin['username']); ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="email" class="form-label">Email</label>
                                                <input type="email" class="form-control" id="email" name="email" 
                                                       value="<?php echo htmlspecialchars($admin['email']); ?>" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <hr>
                                    <h6>Change Password (Optional)</h6>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="current_password" class="form-label">Current Password</label>
                                                <input type="password" class="form-control" id="current_password" name="current_password">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="new_password" class="form-label">New Password</label>
                                                <input type="password" class="form-control" id="new_password" name="new_password">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary">Update Profile</button>
                                </form>
                            </div>
                        </div>

                        <!-- System Information -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">System Information</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Application Version:</strong> <?php echo APP_VERSION; ?></p>
                                        <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
                                        <p><strong>Database:</strong> MySQL</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Environment:</strong> <?php echo $_ENV['APP_ENV'] ?? 'development'; ?></p>
                                        <p><strong>Timezone:</strong> <?php echo date_default_timezone_get(); ?></p>
                                        <p><strong>Last Login:</strong> <?php echo date('M j, Y g:i A', strtotime($admin['updated_at'])); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <!-- Quick Actions -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                            </div>
                            <div class="card-body">
                                <a href="users.php" class="btn btn-outline-primary btn-block mb-2 d-block">
                                    <i class="fas fa-users me-2"></i>
                                    Manage Users
                                </a>
                                <a href="courses.php" class="btn btn-outline-success btn-block mb-2 d-block">
                                    <i class="fas fa-play-circle me-2"></i>
                                    Manage Courses
                                </a>
                                <a href="analytics.php" class="btn btn-outline-info btn-block mb-2 d-block">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    View Analytics
                                </a>
                                <hr>
                                <a href="logout.php" class="btn btn-outline-danger btn-block d-block">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    Logout
                                </a>
                            </div>
                        </div>

                        <!-- Environment Info -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Environment</h6>
                            </div>
                            <div class="card-body">
                                <div class="text-center">
                                    <?php if (($_ENV['APP_ENV'] ?? 'development') === 'development'): ?>
                                    <span class="badge bg-warning text-dark">Development Mode</span>
                                    <?php else: ?>
                                    <span class="badge bg-success">Production Mode</span>
                                    <?php endif; ?>
                                </div>
                                <hr>
                                <small class="text-muted">
                                    <strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?><br>
                                    <strong>Host:</strong> <?php echo $_SERVER['HTTP_HOST'] ?? 'localhost'; ?><br>
                                    <strong>Port:</strong> <?php echo $_SERVER['SERVER_PORT'] ?? '80'; ?>
                                </small>
                            </div>
                        </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <?= $navigation->getNavigationJS() ?>
</body>
</html>

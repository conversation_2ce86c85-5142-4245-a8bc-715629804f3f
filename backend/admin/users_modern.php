<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/Auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/includes/navigation.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Initialize navigation
$navigation = new AdminNavigation('users_modern.php', $_SESSION['admin_name'] ?? 'Admin');

$database = new Database();
$db = $database->getConnection();
$auth = new Auth();

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $userId = (int)$_POST['user_id'];
        
        switch ($_POST['action']) {
            case 'generate_link':
                $authLink = $auth->generateAuthLink($userId, 1);
                if ($authLink) {
                    $message = 'Authentication link generated successfully!';
                    $messageType = 'success';
                    $generatedAuthLink = $authLink;
                } else {
                    $message = 'Error generating authentication link!';
                    $messageType = 'danger';
                }
                break;
                
            case 'revoke_access':
                $stmt = $db->prepare("UPDATE users SET is_active = 0, updated_at = NOW() WHERE id = ?");
                if ($stmt->execute([$userId])) {
                    $message = 'User access revoked successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Error revoking user access!';
                    $messageType = 'danger';
                }
                break;
        }
    }
}

// Fetch users with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$searchCondition = '';
$searchParams = [];

if (!empty($search)) {
    $searchCondition = "WHERE u.full_name LIKE ? OR u.email LIKE ?";
    $searchParams = ["%$search%", "%$search%"];
}

// Get total count
$countQuery = "SELECT COUNT(*) as total FROM users u $searchCondition";
$stmt = $db->prepare($countQuery);
$stmt->execute($searchParams);
$totalUsers = $stmt->fetch()['total'];
$totalPages = ceil($totalUsers / $limit);

// Get users
$query = "
    SELECT u.*, c.title as course_title,
           (SELECT COUNT(*) FROM user_video_progress vp WHERE vp.user_id = u.id AND vp.is_completed = 1) as completed_videos,
           (SELECT COUNT(*) FROM user_video_progress vp WHERE vp.user_id = u.id) as total_videos
    FROM users u
    LEFT JOIN user_courses uc ON u.id = uc.user_id AND uc.is_active = 1
    LEFT JOIN courses c ON uc.course_id = c.id
    $searchCondition
    ORDER BY u.created_at DESC
    LIMIT ? OFFSET ?
";

$params = array_merge($searchParams, [$limit, $offset]);
$stmt = $db->prepare($query);
$stmt->execute($params);
$users = $stmt->fetchAll();

// Calculate statistics
$statsQuery = "
    SELECT 
        COUNT(*) as total_users,
        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_users,
        SUM(CASE WHEN last_login IS NOT NULL THEN 1 ELSE 0 END) as users_with_login,
        AVG(CASE WHEN weight IS NOT NULL AND height IS NOT NULL AND height > 0 
            THEN weight / POWER(height/100, 2) ELSE NULL END) as avg_bmi
    FROM users
";
$stmt = $db->query($statsQuery);
$stats = $stmt->fetch();

function calculateBMI($weight, $height) {
    if ($weight && $height && $height > 0) {
        $heightInMeters = $height / 100;
        return $weight / ($heightInMeters * $heightInMeters);
    }
    return null;
}

function getBMICategory($bmi) {
    if ($bmi === null) return 'Unknown';
    if ($bmi < 18.5) return 'Underweight';
    if ($bmi < 25) return 'Normal';
    if ($bmi < 30) return 'Overweight';
    return 'Obese';
}

function getBMIColor($bmi) {
    if ($bmi === null) return '#6b7280';
    if ($bmi < 18.5) return '#06b6d4';
    if ($bmi < 25) return '#10b981';
    if ($bmi < 30) return '#f59e0b';
    return '#ef4444';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - HomeWorkout Pro Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <?= $navigation->getNavigationCSS() ?>
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --secondary: #8b5cf6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #06b6d4;
            --light: #f8fafc;
            --dark: #1e293b;
            --border: #e2e8f0;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            --gradient-surface: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--surface);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            line-height: 1.6;
            color: var(--text-primary);
            font-size: 14px;
        }

        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        .main-card {
            background: var(--light);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
            overflow: hidden;
            margin-bottom: var(--spacing-xl);
        }

        .header-section {
            background: var(--light);
            border-bottom: 1px solid var(--border);
            padding: var(--spacing-2xl) var(--spacing-xl);
            position: relative;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dashboard-title {
            font-size: 2rem;
            font-weight: 700;
            margin: 0 0 var(--spacing-xs) 0;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .dashboard-title i {
            color: var(--primary);
            font-size: 1.75rem;
        }

        .dashboard-subtitle {
            font-size: 1rem;
            margin: 0;
            font-weight: 400;
            color: var(--text-secondary);
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn-premium {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 0.875rem 1.5rem;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            backdrop-filter: blur(10px);
        }

        .btn-premium:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin: -1rem 2rem 2rem 2rem;
            position: relative;
            z-index: 2;
        }

        .stat-card {
            background: var(--gradient-surface);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-lg);
        }

        .stat-card:hover::before {
            transform: scaleX(1);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1rem;
            background: var(--gradient-primary);
            color: white;
            box-shadow: 0 8px 16px rgba(99, 102, 241, 0.3);
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--dark);
            margin: 0;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #64748b;
            margin: 0.5rem 0 0 0;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .content-section {
            padding: var(--spacing-xl);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-xl);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .section-title i {
            color: var(--primary);
            font-size: 1.125rem;
        }

        .search-container {
            display: flex;
            gap: var(--spacing-md);
            align-items: center;
            margin-bottom: var(--spacing-xl);
        }

        .search-box {
            flex: 1;
            max-width: 400px;
            background: var(--light);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
        }

        .table-container {
            background: var(--light);
            border-radius: var(--radius-md);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border);
        }

        .table {
            margin-bottom: 0;
            width: 100%;
        }

        .table thead th {
            background: var(--gradient-surface);
            border: none;
            font-weight: 600;
            color: #475569;
            padding: 1.25rem 1rem;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
        }

        .table tbody td {
            padding: 1.25rem 1rem;
            border-color: #f1f5f9;
            vertical-align: middle;
            border-bottom: 1px solid var(--border);
        }

        .table tbody tr {
            transition: all 0.2s ease;
        }

        .table tbody tr:hover {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.02), rgba(139, 92, 246, 0.02));
            transform: scale(1.001);
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        .user-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: var(--gradient-primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.125rem;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
            margin-right: 1rem;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            color: var(--dark);
            margin: 0 0 0.25rem 0;
            font-size: 0.875rem;
        }

        .user-email {
            color: #64748b;
            margin: 0;
            font-size: 0.75rem;
        }

        .user-name-link {
            color: inherit;
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .user-name-link:hover {
            color: var(--primary) !important;
            text-decoration: none;
        }

        .status-badge {
            padding: 0.375rem 0.875rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-active {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-inactive {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .btn-action {
            width: 36px;
            height: 36px;
            border-radius: var(--radius);
            border: 1px solid var(--border);
            background: var(--light);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .btn-action:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
            text-decoration: none;
            background: var(--surface);
        }

        .btn-action.btn-view {
            color: var(--info);
            border-color: var(--info);
        }

        .btn-action.btn-view:hover {
            background: rgba(2, 132, 199, 0.1);
            color: var(--info);
        }

        .btn-action.btn-link {
            color: var(--primary);
            border-color: var(--primary);
        }

        .btn-action.btn-link:hover {
            background: rgba(5, 150, 105, 0.1);
            color: var(--primary);
        }

        .btn-action.btn-danger {
            color: var(--accent);
            border-color: var(--accent);
        }

        .btn-action.btn-danger:hover {
            background: rgba(220, 38, 38, 0.1);
            color: var(--accent);
        }

        .pagination-container {
            display: flex;
            justify-content: center;
            margin-top: 2rem;
        }

        .pagination .page-link {
            border: none;
            color: var(--primary);
            padding: 0.75rem 1rem;
            margin: 0 0.25rem;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .pagination .page-link:hover {
            background: rgba(99, 102, 241, 0.1);
            color: var(--primary);
        }

        .pagination .page-item.active .page-link {
            background: var(--gradient-primary);
            color: white;
            box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .alert-info {
            background: rgba(6, 182, 212, 0.1);
            color: var(--info);
            border: 1px solid rgba(6, 182, 212, 0.2);
        }

        @media (max-width: 768px) {
            .dashboard-container {
                padding: 1rem;
            }
            
            .header-section {
                padding: 2rem 1rem;
            }
            
            .header-content {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            
            .dashboard-title {
                font-size: 2rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                margin: -1rem 1rem 2rem 1rem;
            }
            
            .content-section {
                padding: var(--spacing-md);
            }
            
            .search-container {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-box {
                max-width: none;
            }
            
            .section-header {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <?= $navigation->renderNavigation() ?>

    <div class="main-content">
        <div class="dashboard-container">
            <div class="main-card">
                <!-- Header Section -->
                <div class="header-section">
                    <div class="header-content">
                        <div>
                            <button class="mobile-menu-btn" type="button">
                                <i class="bi bi-list"></i>
                            </button>
                            <h1 class="dashboard-title">
                                <i class="bi bi-people-fill"></i>User Management
                            </h1>
                            <p class="dashboard-subtitle">Manage users and their progress</p>
                        </div>
                    </div>
                </div>

            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="bi bi-people-fill"></i>
                    </div>
                    <div class="stat-value"><?= number_format($stats['total_users']) ?></div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, var(--success), #059669);">
                        <i class="bi bi-person-check-fill"></i>
                    </div>
                    <div class="stat-value"><?= number_format($stats['active_users']) ?></div>
                    <div class="stat-label">Active Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, var(--info), #0891b2);">
                        <i class="bi bi-box-arrow-in-right"></i>
                    </div>
                    <div class="stat-value"><?= number_format($stats['users_with_login']) ?></div>
                    <div class="stat-label">Users with Login</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, var(--warning), #d97706);">
                        <i class="bi bi-speedometer2"></i>
                    </div>
                    <div class="stat-value"><?= $stats['avg_bmi'] ? number_format($stats['avg_bmi'], 1) : '--' ?></div>
                    <div class="stat-label">Average BMI</div>
                </div>
            </div>

            <!-- Content Section -->
            <div class="content-section">
                <!-- Alert Messages -->
                <?php if ($message): ?>
                <div class="alert alert-<?= $messageType ?> alert-dismissible fade show" role="alert">
                    <?= $message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Generated Auth Link -->
                <?php if (isset($generatedAuthLink)): ?>
                <div class="alert alert-info" role="alert">
                    <h6><i class="bi bi-link-45deg me-2"></i>Generated Authentication Link:</h6>
                    <div class="input-group mt-2">
                        <input type="text" class="form-control" id="authLink" value="<?= htmlspecialchars($generatedAuthLink) ?>" readonly>
                        <button class="btn btn-outline-primary" type="button" onclick="copyToClipboard('authLink')">
                            <i class="bi bi-clipboard"></i> Copy
                        </button>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Section Header -->
                <div class="section-header">
                    <h2 class="section-title">User Management</h2>
                </div>

                <!-- Search and Filters -->
                <div class="search-container">
                    <form method="GET" class="d-flex w-100 gap-3">
                        <input type="text" name="search" class="search-box" placeholder="Search users by name or email..."
                               value="<?= htmlspecialchars($search) ?>">
                        <button type="submit" class="btn-premium">
                            <i class="bi bi-search"></i>
                            Search
                        </button>
                        <?php if ($search): ?>
                        <a href="?" class="btn-premium" style="background: rgba(239, 68, 68, 0.2); color: var(--danger); border-color: rgba(239, 68, 68, 0.3);">
                            <i class="bi bi-x-circle"></i>
                            Clear
                        </a>
                        <?php endif; ?>
                    </form>
                </div>

                <!-- Users Table -->
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Course</th>
                                <th>Progress</th>
                                <th>BMI</th>
                                <th>Status</th>
                                <th>Last Login</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($users)): ?>
                            <tr>
                                <td colspan="7" class="text-center py-5">
                                    <i class="bi bi-people display-1 text-muted"></i>
                                    <h4 class="mt-3 text-muted">No Users Found</h4>
                                    <p class="text-muted">
                                        <?= $search ? 'No users match your search criteria.' : 'No users have been added yet.' ?>
                                    </p>
                                </td>
                            </tr>
                            <?php else: ?>
                            <?php foreach ($users as $user): ?>
                            <?php
                                $bmi = calculateBMI($user['weight'], $user['height']);
                                $bmiCategory = getBMICategory($bmi);
                                $bmiColor = getBMIColor($bmi);
                                $progressPercentage = $user['total_videos'] > 0 ? ($user['completed_videos'] / $user['total_videos']) * 100 : 0;
                            ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar">
                                            <?= strtoupper(substr($user['full_name'] ?? 'U', 0, 2)) ?>
                                        </div>
                                        <div class="user-info">
                                            <div class="user-name">
                                                <a href="user_view.php?id=<?= $user['id'] ?>" class="user-name-link">
                                                    <?= htmlspecialchars($user['full_name'] ?? '') ?>
                                                </a>
                                            </div>
                                            <div class="user-email"><?= htmlspecialchars($user['email'] ?? '') ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($user['course_title']): ?>
                                        <span class="badge bg-primary"><?= htmlspecialchars($user['course_title']) ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">No course assigned</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="progress" style="width: 80px; height: 6px;">
                                            <div class="progress-bar" style="width: <?= $progressPercentage ?>%; background: var(--success);"></div>
                                        </div>
                                        <small class="text-muted"><?= number_format($progressPercentage, 0) ?>%</small>
                                    </div>
                                    <small class="text-muted"><?= $user['completed_videos'] ?>/<?= $user['total_videos'] ?> videos</small>
                                </td>
                                <td>
                                    <?php if ($bmi): ?>
                                        <div style="color: <?= $bmiColor ?>;">
                                            <strong><?= number_format($bmi, 1) ?></strong>
                                            <br><small><?= $bmiCategory ?></small>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted">--</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="status-badge <?= $user['is_active'] ? 'status-active' : 'status-inactive' ?>">
                                        <?= $user['is_active'] ? 'Active' : 'Inactive' ?>
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?= $user['last_login'] ? date('M d, Y H:i', strtotime($user['last_login'])) : 'Never' ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="user_view.php?id=<?= $user['id'] ?>" class="btn-action btn-view" title="View Profile">
                                            <i class="bi bi-person-lines-fill"></i>
                                        </a>
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="user_id" value="<?= $user['id'] ?>">
                                            <input type="hidden" name="action" value="generate_link">
                                            <button type="submit" class="btn-action btn-link" title="Generate Auth Link">
                                                <i class="bi bi-link-45deg"></i>
                                            </button>
                                        </form>
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="user_id" value="<?= $user['id'] ?>">
                                            <input type="hidden" name="action" value="revoke_access">
                                            <button type="submit" class="btn-action btn-danger" title="Revoke Access"
                                                    onclick="return confirm('Are you sure you want to revoke access for this user?')">
                                                <i class="bi bi-person-x"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                <div class="pagination-container">
                    <nav>
                        <ul class="pagination">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $page - 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?>">
                                    <i class="bi bi-chevron-left"></i>
                                </a>
                            </li>
                            <?php endif; ?>

                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                            <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                <a class="page-link" href="?page=<?= $i ?><?= $search ? '&search=' . urlencode($search) : '' ?>">
                                    <?= $i ?>
                                </a>
                            </li>
                            <?php endfor; ?>

                            <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $page + 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?>">
                                    <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <?= $navigation->getNavigationJS() ?>
    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            element.select();
            element.setSelectionRange(0, 99999);

            try {
                document.execCommand('copy');
                showToast('Copied to clipboard!', 'success');
            } catch (err) {
                navigator.clipboard.writeText(element.value).then(function() {
                    showToast('Copied to clipboard!', 'success');
                }).catch(function(err) {
                    showToast('Failed to copy to clipboard', 'error');
                });
            }
        }

        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'error' ? 'danger' : 'success'} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 250px;';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            document.body.appendChild(toast);

            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>

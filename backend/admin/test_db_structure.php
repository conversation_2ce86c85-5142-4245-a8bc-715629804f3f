<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$database = new Database();
$db = $database->getConnection();

echo "<h1>Database Structure Verification</h1>";

// Test tables and their columns
$tables = [
    'users',
    'user_sessions', 
    'user_video_progress',
    'user_courses',
    'courses',
    'course_videos'
];

foreach ($tables as $table) {
    echo "<h2>Table: $table</h2>";
    
    try {
        $stmt = $db->query("DESCRIBE $table");
        $columns = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        // Test a simple query
        $stmt = $db->query("SELECT COUNT(*) as count FROM $table");
        $count = $stmt->fetch()['count'];
        echo "<p>Total records: $count</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    }
}

// Test specific problematic queries
echo "<h2>Query Tests</h2>";

// Test user sessions query
echo "<h3>User Sessions Query Test</h3>";
try {
    $stmt = $db->query("SELECT * FROM user_sessions ORDER BY started_at DESC LIMIT 5");
    $sessions = $stmt->fetchAll();
    echo "<p style='color: green;'>✅ User sessions query works! Found " . count($sessions) . " sessions.</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ User sessions query failed: " . $e->getMessage() . "</p>";
}

// Test video progress query
echo "<h3>Video Progress Query Test</h3>";
try {
    $stmt = $db->query("
        SELECT vp.*, cv.title as video_title 
        FROM user_video_progress vp
        LEFT JOIN course_videos cv ON vp.video_id = cv.id
        LIMIT 5
    ");
    $progress = $stmt->fetchAll();
    echo "<p style='color: green;'>✅ Video progress query works! Found " . count($progress) . " records.</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Video progress query failed: " . $e->getMessage() . "</p>";
}

// Test user courses query
echo "<h3>User Courses Query Test</h3>";
try {
    $stmt = $db->query("
        SELECT u.*, c.title as course_title
        FROM users u 
        LEFT JOIN user_courses uc ON u.id = uc.user_id AND uc.is_active = 1
        LEFT JOIN courses c ON uc.course_id = c.id 
        LIMIT 5
    ");
    $users = $stmt->fetchAll();
    echo "<p style='color: green;'>✅ User courses query works! Found " . count($users) . " users.</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ User courses query failed: " . $e->getMessage() . "</p>";
}

echo "<h2>Database Structure Verification Complete</h2>";
echo "<p><a href='dashboard_mobile.php'>← Back to Dashboard</a></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background: #f5f5f5;
}

h1, h2, h3 {
    color: #333;
}

table {
    background: white;
    width: 100%;
    max-width: 800px;
}

th {
    background: #f0f0f0;
    padding: 8px;
    text-align: left;
}

td {
    padding: 8px;
    border-bottom: 1px solid #ddd;
}

p {
    background: white;
    padding: 10px;
    border-radius: 5px;
    max-width: 800px;
}
</style>

<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/Auth.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$database = new Database();
$db = $database->getConnection();
$auth = new Auth();

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $userId = (int)$_POST['user_id'];
        
        switch ($_POST['action']) {
            case 'generate_link':
                $authLink = $auth->generateAuthLink($userId, 1);
                if ($authLink) {
                    $message = 'Authentication link generated successfully!';
                    $messageType = 'success';
                    $generatedAuthLink = $authLink;
                } else {
                    $message = 'Error generating authentication link!';
                    $messageType = 'danger';
                }
                break;
                
            case 'revoke_access':
                $stmt = $db->prepare("UPDATE users SET is_active = 0, updated_at = NOW() WHERE id = ?");
                if ($stmt->execute([$userId])) {
                    $message = 'User access revoked successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Error revoking user access!';
                    $messageType = 'danger';
                }
                break;
        }
    }
}

// Fetch users with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$searchCondition = '';
$searchParams = [];

if (!empty($search)) {
    $searchCondition = "WHERE u.full_name LIKE ? OR u.email LIKE ?";
    $searchParams = ["%$search%", "%$search%"];
}

// Get total count
$countQuery = "SELECT COUNT(*) as total FROM users u $searchCondition";
$stmt = $db->prepare($countQuery);
$stmt->execute($searchParams);
$totalUsers = $stmt->fetch()['total'];
$totalPages = ceil($totalUsers / $limit);

// Get users
$query = "
    SELECT u.*, c.title as course_title,
           (SELECT COUNT(*) FROM user_video_progress vp WHERE vp.user_id = u.id AND vp.is_completed = 1) as completed_videos,
           (SELECT COUNT(*) FROM user_video_progress vp WHERE vp.user_id = u.id) as total_videos
    FROM users u 
    LEFT JOIN user_courses uc ON u.id = uc.user_id AND uc.is_active = 1
    LEFT JOIN courses c ON uc.course_id = c.id 
    $searchCondition
    ORDER BY u.created_at DESC 
    LIMIT ? OFFSET ?
";

$params = array_merge($searchParams, [$limit, $offset]);
$stmt = $db->prepare($query);
$stmt->execute($params);
$users = $stmt->fetchAll();

function calculateBMI($weight, $height) {
    if ($weight && $height && $height > 0) {
        $heightInMeters = $height / 100;
        return $weight / ($heightInMeters * $heightInMeters);
    }
    return null;
}

function getBMIColor($bmi) {
    if ($bmi === null) return '#6b7280';
    if ($bmi < 18.5) return '#06b6d4';
    if ($bmi < 25) return '#10b981';
    if ($bmi < 30) return '#f59e0b';
    return '#ef4444';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Users - HomeWorkout Pro Mobile</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary: #10b981;
            --primary-dark: #059669;
            --secondary: #06b6d4;
            --accent: #f59e0b;
            --success: #22c55e;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #06b6d4;
            --light: #f8fafc;
            --dark: #1e293b;
            --border: #e2e8f0;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --bottom-nav-height: 80px;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--light);
            margin: 0;
            padding: 0;
            line-height: 1.6;
            color: var(--dark);
            padding-bottom: var(--bottom-nav-height);
        }

        .mobile-container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        .mobile-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .header-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin: 0;
        }

        .search-container {
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 0.875rem;
            backdrop-filter: blur(10px);
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .search-input:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.2);
        }

        .content-area {
            padding: 1rem;
            padding-bottom: calc(var(--bottom-nav-height) + 1rem);
        }

        .user-card {
            background: white;
            border-radius: 16px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
            transition: all 0.3s ease;
        }

        .user-card:active {
            transform: scale(0.98);
        }

        .user-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .user-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.125rem;
            margin-right: 1rem;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            margin: 0 0 0.25rem 0;
            font-size: 1rem;
            color: var(--dark);
        }

        .user-email {
            color: #64748b;
            margin: 0;
            font-size: 0.875rem;
        }

        .user-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-active {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-inactive {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .user-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 1.125rem;
            font-weight: 700;
            margin: 0;
            color: var(--primary);
        }

        .stat-label {
            font-size: 0.75rem;
            color: #64748b;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .user-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
        }

        .btn-action {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            border: 1px solid var(--border);
            background: white;
            color: #64748b;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-action:hover {
            text-decoration: none;
        }

        .btn-action:active {
            transform: scale(0.95);
        }

        .btn-view {
            color: var(--info);
            border-color: rgba(6, 182, 212, 0.2);
        }

        .btn-view:hover {
            background: rgba(6, 182, 212, 0.1);
            color: var(--info);
        }

        .btn-link {
            color: var(--primary);
            border-color: rgba(16, 185, 129, 0.2);
        }

        .btn-link:hover {
            background: rgba(16, 185, 129, 0.1);
            color: var(--primary);
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: var(--bottom-nav-height);
            background: white;
            border-top: 1px solid var(--border);
            display: flex;
            align-items: center;
            justify-content: space-around;
            z-index: 1000;
            box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #64748b;
            transition: all 0.2s ease;
            padding: 0.5rem;
            border-radius: 12px;
            min-width: 60px;
        }

        .nav-item.active {
            color: var(--primary);
            background: rgba(16, 185, 129, 0.1);
        }

        .nav-item:hover {
            color: var(--primary);
            text-decoration: none;
        }

        .nav-item:active {
            transform: scale(0.95);
        }

        .nav-icon {
            font-size: 1.25rem;
            margin-bottom: 0.25rem;
        }

        .nav-label {
            font-size: 0.75rem;
            font-weight: 500;
        }

        .pagination-container {
            display: flex;
            justify-content: center;
            margin-top: 2rem;
            gap: 0.5rem;
        }

        .page-btn {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border);
            background: white;
            color: var(--primary);
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .page-btn.active {
            background: var(--primary);
            color: white;
        }

        .page-btn:hover {
            text-decoration: none;
            background: rgba(16, 185, 129, 0.1);
            color: var(--primary);
        }

        .page-btn.active:hover {
            background: var(--primary-dark);
            color: white;
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
        }

        .empty-icon {
            font-size: 4rem;
            color: #d1d5db;
            margin-bottom: 1rem;
        }

        .empty-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 0.5rem;
        }

        .empty-subtitle {
            color: #64748b;
            margin: 0;
        }

        @media (min-width: 768px) {
            .mobile-container {
                max-width: 768px;
            }
            
            .user-stats {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        @media (max-width: 480px) {
            .content-area {
                padding: 0.75rem;
            }
            
            .user-card {
                padding: 0.75rem;
            }
            
            .user-actions {
                flex-direction: column;
            }
            
            .btn-action {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- Mobile Header -->
        <div class="mobile-header">
            <div class="header-content">
                <h1 class="header-title">
                    <i class="bi bi-people-fill me-2"></i>Users
                </h1>
                <div class="text-white-50">
                    <?= number_format($totalUsers) ?> total
                </div>
            </div>

            <!-- Search -->
            <div class="search-container">
                <form method="GET">
                    <input type="text" name="search" class="search-input"
                           placeholder="Search users..."
                           value="<?= htmlspecialchars($search) ?>"
                           onchange="this.form.submit()">
                </form>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Alert Messages -->
            <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?>">
                <?= $message ?>
            </div>
            <?php endif; ?>

            <!-- Generated Auth Link -->
            <?php if (isset($generatedAuthLink)): ?>
            <div class="alert alert-success">
                <h6><i class="bi bi-link-45deg me-2"></i>Generated Link:</h6>
                <div class="input-group mt-2">
                    <input type="text" class="form-control" id="authLink" value="<?= htmlspecialchars($generatedAuthLink) ?>" readonly>
                    <button class="btn btn-outline-success" type="button" onclick="copyToClipboard('authLink')">
                        <i class="bi bi-clipboard"></i>
                    </button>
                </div>
            </div>
            <?php endif; ?>

            <!-- Users List -->
            <?php if (empty($users)): ?>
            <div class="empty-state">
                <i class="bi bi-people empty-icon"></i>
                <h3 class="empty-title">No Users Found</h3>
                <p class="empty-subtitle">
                    <?= $search ? 'No users match your search criteria.' : 'No users have been added yet.' ?>
                </p>
            </div>
            <?php else: ?>
            <?php foreach ($users as $user): ?>
            <?php
                $bmi = calculateBMI($user['weight'], $user['height']);
                $bmiColor = getBMIColor($bmi);
                $progressPercentage = $user['total_videos'] > 0 ? ($user['completed_videos'] / $user['total_videos']) * 100 : 0;
            ?>
            <div class="user-card">
                <div class="user-header">
                    <div class="user-avatar">
                        <?= strtoupper(substr($user['full_name'] ?? 'U', 0, 2)) ?>
                    </div>
                    <div class="user-info">
                        <div class="user-name"><?= htmlspecialchars($user['full_name'] ?? '') ?></div>
                        <div class="user-email"><?= htmlspecialchars($user['email'] ?? '') ?></div>
                    </div>
                    <div class="user-status <?= $user['is_active'] ? 'status-active' : 'status-inactive' ?>">
                        <?= $user['is_active'] ? 'Active' : 'Inactive' ?>
                    </div>
                </div>

                <div class="user-stats">
                    <div class="stat-item">
                        <div class="stat-value"><?= number_format($progressPercentage, 0) ?>%</div>
                        <div class="stat-label">Progress</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value"><?= $user['completed_videos'] ?>/<?= $user['total_videos'] ?></div>
                        <div class="stat-label">Videos</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" style="color: <?= $bmiColor ?>;">
                            <?= $bmi ? number_format($bmi, 1) : '--' ?>
                        </div>
                        <div class="stat-label">BMI</div>
                    </div>
                </div>

                <div class="user-actions">
                    <a href="user_view.php?id=<?= $user['id'] ?>" class="btn-action btn-view">
                        <i class="bi bi-person-lines-fill"></i>
                        View Profile
                    </a>
                    <form method="POST" class="d-inline">
                        <input type="hidden" name="user_id" value="<?= $user['id'] ?>">
                        <input type="hidden" name="action" value="generate_link">
                        <button type="submit" class="btn-action btn-link">
                            <i class="bi bi-link-45deg"></i>
                            Auth Link
                        </button>
                    </form>
                </div>
            </div>
            <?php endforeach; ?>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
            <div class="pagination-container">
                <?php if ($page > 1): ?>
                <a href="?page=<?= $page - 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?>" class="page-btn">
                    <i class="bi bi-chevron-left"></i>
                </a>
                <?php endif; ?>

                <?php for ($i = max(1, $page - 1); $i <= min($totalPages, $page + 1); $i++): ?>
                <a href="?page=<?= $i ?><?= $search ? '&search=' . urlencode($search) : '' ?>"
                   class="page-btn <?= $i === $page ? 'active' : '' ?>">
                    <?= $i ?>
                </a>
                <?php endfor; ?>

                <?php if ($page < $totalPages): ?>
                <a href="?page=<?= $page + 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?>" class="page-btn">
                    <i class="bi bi-chevron-right"></i>
                </a>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            <?php endif; ?>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <a href="dashboard_mobile.php" class="nav-item">
                <i class="bi bi-house-fill nav-icon"></i>
                <span class="nav-label">Home</span>
            </a>
            <a href="users_mobile.php" class="nav-item active">
                <i class="bi bi-people-fill nav-icon"></i>
                <span class="nav-label">Users</span>
            </a>
            <a href="dashboard_weightloss.php" class="nav-item">
                <i class="bi bi-graph-up nav-icon"></i>
                <span class="nav-label">Analytics</span>
            </a>
            <a href="courses.php" class="nav-item">
                <i class="bi bi-play-circle-fill nav-icon"></i>
                <span class="nav-label">Courses</span>
            </a>
            <a href="logout.php" class="nav-item">
                <i class="bi bi-box-arrow-right nav-icon"></i>
                <span class="nav-label">Logout</span>
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            element.select();
            element.setSelectionRange(0, 99999);

            try {
                document.execCommand('copy');
                showToast('Copied to clipboard!', 'success');
            } catch (err) {
                navigator.clipboard.writeText(element.value).then(function() {
                    showToast('Copied to clipboard!', 'success');
                }).catch(function(err) {
                    showToast('Failed to copy to clipboard', 'error');
                });
            }
        }

        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'error' ? 'danger' : 'success'} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 250px;';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            document.body.appendChild(toast);

            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 3000);
        }

        // Add touch feedback for mobile interactions
        document.querySelectorAll('.user-card, .btn-action, .nav-item').forEach(element => {
            element.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.98)';
            });

            element.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // Auto-submit search on input
        const searchInput = document.querySelector('.search-input');
        let searchTimeout;

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.form.submit();
            }, 500);
        });
    </script>
</body>
</html>

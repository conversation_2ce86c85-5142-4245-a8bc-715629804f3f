<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/Auth.php';
require_once __DIR__ . '/includes/navigation.php';
require_once __DIR__ . '/../includes/functions.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$database = new Database();
$db = $database->getConnection();

// Get course ID from URL
$courseId = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;

if (!$courseId) {
    header('Location: courses.php');
    exit;
}

// Get course details
$stmt = $db->prepare("SELECT * FROM courses WHERE id = ?");
$stmt->execute([$courseId]);
$course = $stmt->fetch();

if (!$course) {
    header('Location: courses.php');
    exit;
}

// Initialize navigation
$navigation = new AdminNavigation('courses.php', $_SESSION['admin_name'] ?? 'Admin');

$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add_video':
            $title = sanitizeInput($_POST['title']);
            $description = sanitizeInput($_POST['description']);
            $vimeoVideoId = sanitizeInput($_POST['vimeo_video_id']);
            $unlockDay = (int)$_POST['unlock_day'];
            $orderIndex = (int)$_POST['order_index'];
            
            if (!empty($title) && !empty($vimeoVideoId)) {
                try {
                    // Generate Vimeo embed URL
                    $vimeoEmbedUrl = "https://player.vimeo.com/video/{$vimeoVideoId}";
                    
                    $stmt = $db->prepare("
                        INSERT INTO course_videos (course_id, title, description, vimeo_video_id, vimeo_embed_url, unlock_day, order_index) 
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([$courseId, $title, $description, $vimeoVideoId, $vimeoEmbedUrl, $unlockDay, $orderIndex]);
                    
                    $message = 'Video added successfully!';
                    $messageType = 'success';
                    
                } catch (Exception $e) {
                    $message = 'Error adding video: ' . $e->getMessage();
                    $messageType = 'danger';
                }
            } else {
                $message = 'Video title and Vimeo ID are required';
                $messageType = 'danger';
            }
            break;
            
        case 'delete_video':
            $videoId = (int)$_POST['video_id'];
            try {
                $stmt = $db->prepare("DELETE FROM course_videos WHERE id = ? AND course_id = ?");
                $stmt->execute([$videoId, $courseId]);
                $message = 'Video deleted successfully!';
                $messageType = 'success';
            } catch (Exception $e) {
                $message = 'Error deleting video: ' . $e->getMessage();
                $messageType = 'danger';
            }
            break;
            
        case 'update_video':
            $videoId = (int)$_POST['video_id'];
            $title = sanitizeInput($_POST['title']);
            $description = sanitizeInput($_POST['description']);
            $vimeoVideoId = sanitizeInput($_POST['vimeo_video_id']);
            $unlockDay = (int)$_POST['unlock_day'];
            $orderIndex = (int)$_POST['order_index'];
            $isActive = isset($_POST['is_active']) ? 1 : 0;
            
            if (!empty($title) && !empty($vimeoVideoId)) {
                try {
                    $vimeoEmbedUrl = "https://player.vimeo.com/video/{$vimeoVideoId}";
                    
                    $stmt = $db->prepare("
                        UPDATE course_videos 
                        SET title = ?, description = ?, vimeo_video_id = ?, vimeo_embed_url = ?, 
                            unlock_day = ?, order_index = ?, is_active = ?
                        WHERE id = ? AND course_id = ?
                    ");
                    $stmt->execute([$title, $description, $vimeoVideoId, $vimeoEmbedUrl, $unlockDay, $orderIndex, $isActive, $videoId, $courseId]);
                    
                    $message = 'Video updated successfully!';
                    $messageType = 'success';
                    
                } catch (Exception $e) {
                    $message = 'Error updating video: ' . $e->getMessage();
                    $messageType = 'danger';
                }
            } else {
                $message = 'Video title and Vimeo ID are required';
                $messageType = 'danger';
            }
            break;
    }
}

// Get all videos for this course
$stmt = $db->prepare("
    SELECT cv.*, 
           COUNT(uvp.id) as view_count,
           AVG(uvp.completion_percentage) as avg_completion
    FROM course_videos cv
    LEFT JOIN user_video_progress uvp ON cv.id = uvp.video_id
    WHERE cv.course_id = ?
    GROUP BY cv.id
    ORDER BY cv.order_index ASC, cv.unlock_day ASC
");
$stmt->execute([$courseId]);
$videos = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Videos - <?php echo htmlspecialchars($course['title']); ?> - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <?= $navigation->getNavigationCSS() ?>
    <style>
        /* Use same minimalistic theme as other pages */
        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        .main-card {
            background: var(--light);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
            overflow: hidden;
            margin-bottom: var(--spacing-xl);
        }

        .header-section {
            background: var(--light);
            border-bottom: 1px solid var(--border);
            padding: var(--spacing-2xl) var(--spacing-xl);
            position: relative;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dashboard-title {
            font-size: 2rem;
            font-weight: 700;
            margin: 0 0 var(--spacing-xs) 0;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .dashboard-title i {
            color: var(--primary);
            font-size: 1.75rem;
        }

        .dashboard-subtitle {
            font-size: 1rem;
            margin: 0;
            font-weight: 400;
            color: var(--text-secondary);
        }

        .content-section {
            padding: var(--spacing-xl);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 var(--spacing-lg) 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .section-title i {
            color: var(--primary);
            font-size: 1.125rem;
        }

        .table-container {
            background: var(--light);
            border-radius: var(--radius-md);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border);
        }

        .btn-minimal {
            background: var(--light);
            color: var(--text-primary);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: var(--spacing-sm) var(--spacing-md);
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .btn-minimal:hover {
            background: var(--surface);
            color: var(--primary);
            text-decoration: none;
            box-shadow: var(--shadow-sm);
        }

        .btn-primary {
            background: var(--primary);
            color: white;
            border: 1px solid var(--primary);
        }

        .btn-primary:hover {
            background: var(--primary-light);
            border-color: var(--primary-light);
            color: white;
        }

        .breadcrumb-nav {
            background: var(--surface);
            border-radius: var(--radius);
            padding: var(--spacing-sm) var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .video-preview {
            width: 60px;
            height: 40px;
            background: var(--surface);
            border-radius: var(--radius-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
        }

        @media (max-width: 768px) {
            .dashboard-container {
                padding: var(--spacing-md);
            }

            .header-section {
                padding: var(--spacing-xl) var(--spacing-md);
            }

            .content-section {
                padding: var(--spacing-md);
            }
        }
    </style>
</head>
<body>
    <?= $navigation->renderNavigation() ?>

    <div class="main-content">
        <div class="dashboard-container">
            <div class="main-card">
                <!-- Header Section -->
                <div class="header-section">
                    <div class="header-content">
                        <div>
                            <button class="mobile-menu-btn" type="button">
                                <i class="bi bi-list"></i>
                            </button>
                            <h1 class="dashboard-title">
                                <i class="bi bi-collection-play"></i>Manage Videos
                            </h1>
                            <p class="dashboard-subtitle">Course: <?php echo htmlspecialchars($course['title']); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Content Section -->
                <div class="content-section">
                    <!-- Breadcrumb -->
                    <nav class="breadcrumb-nav">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="courses.php">Courses</a></li>
                            <li class="breadcrumb-item active"><?php echo htmlspecialchars($course['title']); ?></li>
                        </ol>
                    </nav>

                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="section-title">
                            <i class="bi bi-play-circle"></i>
                            Course Videos
                        </h2>
                        <button type="button" class="btn-minimal btn-primary" data-bs-toggle="modal" data-bs-target="#addVideoModal">
                            <i class="bi bi-plus-circle"></i>
                            Add New Video
                        </button>
                    </div>

                    <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php endif; ?>

                    <!-- Videos Table -->
                    <div class="table-container">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle mb-0">
                                <thead>
                                    <tr>
                                        <th>Preview</th>
                                        <th>Video Title</th>
                                        <th>Unlock Day</th>
                                        <th>Order</th>
                                        <th>Views</th>
                                        <th>Avg Completion</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($videos)): ?>
                                    <tr>
                                        <td colspan="8" class="text-center text-muted">
                                            No videos found. <a href="#" data-bs-toggle="modal" data-bs-target="#addVideoModal">Add your first video</a>
                                        </td>
                                    </tr>
                                    <?php else: ?>
                                    <?php foreach ($videos as $video): ?>
                                    <tr>
                                        <td>
                                            <div class="video-preview">
                                                <i class="bi bi-play-fill"></i>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <div class="fw-bold"><?php echo htmlspecialchars($video['title']); ?></div>
                                                <?php if ($video['description']): ?>
                                                <small class="text-muted"><?php echo htmlspecialchars(substr($video['description'], 0, 60)); ?>...</small>
                                                <?php endif; ?>
                                                <br><small class="text-info">Vimeo ID: <?php echo htmlspecialchars($video['vimeo_video_id']); ?></small>
                                            </div>
                                        </td>
                                        <td>Day <?php echo $video['unlock_day']; ?></td>
                                        <td><?php echo $video['order_index']; ?></td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $video['view_count'] ?? 0; ?> views</span>
                                        </td>
                                        <td>
                                            <?php
                                            $avgCompletion = $video['avg_completion'] ?? 0;
                                            $badgeClass = $avgCompletion >= 80 ? 'bg-success' : ($avgCompletion >= 50 ? 'bg-warning' : 'bg-secondary');
                                            ?>
                                            <span class="badge <?php echo $badgeClass; ?>"><?php echo number_format($avgCompletion, 1); ?>%</span>
                                        </td>
                                        <td>
                                            <?php if ($video['is_active']): ?>
                                                <span class="badge bg-success">Active</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Inactive</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <!-- Edit Button -->
                                                <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="tooltip" data-bs-placement="top" title="Edit Video" onclick="openEditVideoModal(<?php echo htmlspecialchars(json_encode($video)); ?>)">
                                                    <i class="bi bi-pencil-square"></i>
                                                </button>
                                                <!-- Preview Button -->
                                                <button type="button" class="btn btn-sm btn-outline-info" data-bs-toggle="tooltip" data-bs-placement="top" title="Preview Video" onclick="previewVideo('<?php echo $video['vimeo_video_id']; ?>')">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                <!-- Delete Button -->
                                                <form method="POST" action="" style="display:inline;" onsubmit="return confirm('Are you sure you want to delete this video?');">
                                                    <input type="hidden" name="action" value="delete_video">
                                                    <input type="hidden" name="video_id" value="<?php echo $video['id']; ?>">
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" data-bs-toggle="tooltip" data-bs-placement="top" title="Delete Video">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Video Modal -->
    <div class="modal fade" id="addVideoModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Video</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_video">

                        <div class="mb-3">
                            <label for="title" class="form-label">Video Title *</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="vimeo_video_id" class="form-label">Vimeo Video ID *</label>
                            <input type="text" class="form-control" id="vimeo_video_id" name="vimeo_video_id" required>
                            <div class="form-text">Enter the Vimeo video ID (e.g., 123456789)</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="unlock_day" class="form-label">Unlock Day</label>
                                    <input type="number" class="form-control" id="unlock_day" name="unlock_day" value="1" min="1">
                                    <div class="form-text">Day when video becomes available</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="order_index" class="form-label">Order Index</label>
                                    <input type="number" class="form-control" id="order_index" name="order_index" value="1" min="0">
                                    <div class="form-text">Display order in the course</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Video</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Video Modal -->
    <div class="modal fade" id="editVideoModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Video</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update_video">
                        <input type="hidden" name="video_id" id="edit_video_id">

                        <div class="mb-3">
                            <label for="edit_title" class="form-label">Video Title *</label>
                            <input type="text" class="form-control" id="edit_title" name="title" required>
                        </div>

                        <div class="mb-3">
                            <label for="edit_description" class="form-label">Description</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="edit_vimeo_video_id" class="form-label">Vimeo Video ID *</label>
                            <input type="text" class="form-control" id="edit_vimeo_video_id" name="vimeo_video_id" required>
                            <div class="form-text">Enter the Vimeo video ID (e.g., 123456789)</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_unlock_day" class="form-label">Unlock Day</label>
                                    <input type="number" class="form-control" id="edit_unlock_day" name="unlock_day" min="1">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_order_index" class="form-label">Order Index</label>
                                    <input type="number" class="form-control" id="edit_order_index" name="order_index" min="0">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                                <label class="form-check-label" for="edit_is_active">
                                    Video is active
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Video</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Video Preview Modal -->
    <div class="modal fade" id="previewVideoModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Video Preview</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="ratio ratio-16x9">
                        <iframe id="previewFrame" src="" frameborder="0" allow="autoplay; fullscreen; picture-in-picture" allowfullscreen></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <?= $navigation->getNavigationJS() ?>
    <script>
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        })

        // Open edit video modal
        function openEditVideoModal(video) {
            document.getElementById('edit_video_id').value = video.id;
            document.getElementById('edit_title').value = video.title;
            document.getElementById('edit_description').value = video.description || '';
            document.getElementById('edit_vimeo_video_id').value = video.vimeo_video_id;
            document.getElementById('edit_unlock_day').value = video.unlock_day;
            document.getElementById('edit_order_index').value = video.order_index;
            document.getElementById('edit_is_active').checked = video.is_active == 1;

            var editModal = new bootstrap.Modal(document.getElementById('editVideoModal'));
            editModal.show();
        }

        // Preview video
        function previewVideo(vimeoId) {
            const previewFrame = document.getElementById('previewFrame');
            previewFrame.src = `https://player.vimeo.com/video/${vimeoId}`;

            var previewModal = new bootstrap.Modal(document.getElementById('previewVideoModal'));
            previewModal.show();

            // Clear iframe when modal is closed
            document.getElementById('previewVideoModal').addEventListener('hidden.bs.modal', function () {
                previewFrame.src = '';
            });
        }
    </script>
</body>
</html>

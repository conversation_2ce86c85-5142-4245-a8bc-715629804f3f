<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/Auth.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Mobile detection function
function isMobile() {
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $mobileKeywords = [
        'Mobile', 'Android', 'iPhone', 'iPad', 'iPod', 'BlackBerry',
        'Windows Phone', 'Opera Mini', 'IEMobile', 'Mobile Safari'
    ];

    foreach ($mobileKeywords as $keyword) {
        if (stripos($userAgent, $keyword) !== false) {
            return true;
        }
    }

    return false;
}

// Check for mobile override parameter
$forceMobile = isset($_GET['mobile']) && $_GET['mobile'] === '1';
$forceDesktop = isset($_GET['desktop']) && $_GET['desktop'] === '1';

// Determine interface type
$isMobileDevice = isMobile();
$useMobileInterface = ($isMobileDevice && !$forceDesktop) || $forceMobile;

// Redirect to appropriate interface
if ($useMobileInterface) {
    header('Location: dashboard_mobile.php');
    exit;
} else {
    header('Location: dashboard_weightloss.php');
    exit;
}

?>

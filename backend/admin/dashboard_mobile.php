<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/Auth.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$database = new Database();
$db = $database->getConnection();

// Fetch comprehensive statistics (same as weight loss dashboard)
$stats = [];

// User Statistics
$stmt = $db->query("
    SELECT 
        COUNT(*) as total_users,
        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_users,
        SUM(CASE WHEN last_login IS NOT NULL THEN 1 ELSE 0 END) as users_with_login,
        SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as new_users_week
    FROM users
");
$userStats = $stmt->fetch();

// Weight Loss Statistics
$stmt = $db->query("
    SELECT 
        COUNT(CASE WHEN weight IS NOT NULL AND target_weight IS NOT NULL THEN 1 END) as users_with_goals,
        AVG(CASE WHEN weight IS NOT NULL AND height IS NOT NULL AND height > 0 
            THEN weight / POWER(height/100, 2) ELSE NULL END) as avg_bmi,
        AVG(CASE WHEN weight IS NOT NULL AND target_weight IS NOT NULL 
            THEN weight - target_weight ELSE NULL END) as avg_weight_to_lose,
        COUNT(CASE WHEN weight IS NOT NULL AND target_weight IS NOT NULL AND weight <= target_weight 
            THEN 1 END) as users_reached_goal
    FROM users
");
$weightStats = $stmt->fetch();

// Course Progress Statistics
$stmt = $db->query("
    SELECT 
        COUNT(DISTINCT u.id) as users_in_courses,
        AVG(CASE WHEN vp.total_videos > 0 THEN (vp.completed_videos / vp.total_videos) * 100 ELSE 0 END) as avg_progress,
        SUM(vp.completed_videos) as total_videos_watched
    FROM users u
    LEFT JOIN user_courses uc ON u.id = uc.user_id AND uc.is_active = 1
    LEFT JOIN (
        SELECT 
            user_id,
            COUNT(*) as total_videos,
            SUM(CASE WHEN is_completed = 1 THEN 1 ELSE 0 END) as completed_videos
        FROM user_video_progress 
        GROUP BY user_id
    ) vp ON u.id = vp.user_id
    WHERE uc.course_id IS NOT NULL
");
$courseStats = $stmt->fetch();

// Recent users for quick access
$stmt = $db->query("
    SELECT u.id, u.full_name, u.email, u.is_active, u.created_at,
           CASE WHEN u.weight IS NOT NULL AND u.height IS NOT NULL AND u.height > 0 
               THEN u.weight / POWER(u.height/100, 2) 
               ELSE NULL 
           END as bmi
    FROM users u 
    ORDER BY u.created_at DESC 
    LIMIT 5
");
$recentUsers = $stmt->fetchAll();

function getBMIColor($bmi) {
    if ($bmi === null) return '#6b7280';
    if ($bmi < 18.5) return '#06b6d4';
    if ($bmi < 25) return '#10b981';
    if ($bmi < 30) return '#f59e0b';
    return '#ef4444';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HomeWorkout Pro - Mobile Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary: #10b981;
            --primary-dark: #059669;
            --secondary: #06b6d4;
            --accent: #f59e0b;
            --success: #22c55e;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #06b6d4;
            --light: #f8fafc;
            --dark: #1e293b;
            --border: #e2e8f0;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            --bottom-nav-height: 80px;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--light);
            margin: 0;
            padding: 0;
            line-height: 1.6;
            color: var(--dark);
            padding-bottom: var(--bottom-nav-height);
        }

        .mobile-container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        .mobile-header {
            background: var(--gradient-primary);
            color: white;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin: 0;
        }

        .header-subtitle {
            font-size: 0.875rem;
            opacity: 0.9;
            margin: 0;
        }

        .header-avatar {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1rem;
        }

        .content-area {
            padding: 1rem;
            padding-bottom: calc(var(--bottom-nav-height) + 1rem);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:active {
            transform: scale(0.98);
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            margin: 0 auto 1rem auto;
            color: white;
        }

        .stat-value {
            font-size: 1.75rem;
            font-weight: 800;
            color: var(--dark);
            margin: 0 0 0.25rem 0;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.75rem;
            color: #64748b;
            margin: 0;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .section-title {
            font-size: 1.125rem;
            font-weight: 700;
            color: var(--dark);
            margin: 0 0 1rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .action-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
            text-decoration: none;
            color: var(--dark);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .action-card:hover {
            color: var(--dark);
            text-decoration: none;
        }

        .action-card:active {
            transform: scale(0.98);
        }

        .action-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            margin-bottom: 1rem;
            color: white;
        }

        .action-title {
            font-weight: 600;
            margin: 0;
            font-size: 0.875rem;
        }

        .recent-users {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
        }

        .user-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border);
            text-decoration: none;
            color: var(--dark);
        }

        .user-item:last-child {
            border-bottom: none;
        }

        .user-item:hover {
            color: var(--dark);
            text-decoration: none;
        }

        .user-item:active {
            background: rgba(16, 185, 129, 0.05);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--gradient-primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 0.875rem;
            margin-right: 1rem;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            margin: 0 0 0.25rem 0;
            font-size: 0.875rem;
        }

        .user-email {
            color: #64748b;
            margin: 0;
            font-size: 0.75rem;
        }

        .user-status {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-left: 0.5rem;
        }

        .status-active {
            background: var(--success);
        }

        .status-inactive {
            background: var(--danger);
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: var(--bottom-nav-height);
            background: white;
            border-top: 1px solid var(--border);
            display: flex;
            align-items: center;
            justify-content: space-around;
            z-index: 1000;
            box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #64748b;
            transition: all 0.2s ease;
            padding: 0.5rem;
            border-radius: 12px;
            min-width: 60px;
        }

        .nav-item.active {
            color: var(--primary);
            background: rgba(16, 185, 129, 0.1);
        }

        .nav-item:hover {
            color: var(--primary);
            text-decoration: none;
        }

        .nav-item:active {
            transform: scale(0.95);
        }

        .nav-icon {
            font-size: 1.25rem;
            margin-bottom: 0.25rem;
        }

        .nav-label {
            font-size: 0.75rem;
            font-weight: 500;
        }

        .table-responsive {
            border-radius: 16px;
            overflow: hidden;
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
            background: white;
        }

        .table {
            margin: 0;
            font-size: 0.875rem;
        }

        .table th {
            background: var(--light);
            border: none;
            font-weight: 600;
            color: #475569;
            padding: 1rem 0.75rem;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .table td {
            padding: 1rem 0.75rem;
            border-color: var(--border);
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background: rgba(16, 185, 129, 0.02);
        }

        @media (min-width: 768px) {
            .mobile-container {
                max-width: 768px;
            }
            
            .stats-grid {
                grid-template-columns: repeat(4, 1fr);
            }
            
            .quick-actions {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        @media (max-width: 480px) {
            .content-area {
                padding: 0.75rem;
            }
            
            .stat-card {
                padding: 1rem;
            }
            
            .stat-value {
                font-size: 1.5rem;
            }
            
            .recent-users {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- Mobile Header -->
        <div class="mobile-header">
            <div class="header-content">
                <div>
                    <h1 class="header-title">
                        <i class="bi bi-heart-pulse me-2"></i>HomeWorkout Pro
                    </h1>
                    <p class="header-subtitle">Weight Loss Dashboard</p>
                </div>
                <div class="header-avatar">
                    <?= strtoupper(substr($_SESSION['admin_name'] ?? 'Admin', 0, 2)) ?>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Statistics Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--gradient-primary);">
                        <i class="bi bi-people-fill"></i>
                    </div>
                    <div class="stat-value"><?= number_format($userStats['total_users']) ?></div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, var(--success), #16a34a);">
                        <i class="bi bi-person-check-fill"></i>
                    </div>
                    <div class="stat-value"><?= number_format($userStats['active_users']) ?></div>
                    <div class="stat-label">Active Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, var(--warning), #d97706);">
                        <i class="bi bi-bullseye"></i>
                    </div>
                    <div class="stat-value"><?= number_format($weightStats['users_with_goals']) ?></div>
                    <div class="stat-label">With Goals</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, var(--info), #0891b2);">
                        <i class="bi bi-speedometer2"></i>
                    </div>
                    <div class="stat-value"><?= $weightStats['avg_bmi'] ? number_format($weightStats['avg_bmi'], 1) : '--' ?></div>
                    <div class="stat-label">Avg BMI</div>
                </div>
            </div>

            <!-- Quick Actions -->
            <h2 class="section-title">
                <i class="bi bi-lightning-charge-fill" style="color: var(--primary);"></i>
                Quick Actions
            </h2>
            <div class="quick-actions">
                <a href="users_modern.php" class="action-card">
                    <div class="action-icon" style="background: var(--gradient-primary);">
                        <i class="bi bi-people-fill"></i>
                    </div>
                    <div class="action-title">Manage Users</div>
                </a>
                <a href="dashboard_weightloss.php" class="action-card">
                    <div class="action-icon" style="background: linear-gradient(135deg, var(--secondary), #0284c7);">
                        <i class="bi bi-graph-up-arrow"></i>
                    </div>
                    <div class="action-title">Full Dashboard</div>
                </a>
                <a href="courses.php" class="action-card">
                    <div class="action-icon" style="background: linear-gradient(135deg, var(--accent), #d97706);">
                        <i class="bi bi-play-circle-fill"></i>
                    </div>
                    <div class="action-title">Courses</div>
                </a>
                <a href="analytics.php" class="action-card">
                    <div class="action-icon" style="background: linear-gradient(135deg, var(--success), #16a34a);">
                        <i class="bi bi-bar-chart-fill"></i>
                    </div>
                    <div class="action-title">Analytics</div>
                </a>
            </div>

            <!-- Recent Users -->
            <h2 class="section-title">
                <i class="bi bi-clock-history" style="color: var(--primary);"></i>
                Recent Users
            </h2>
            <div class="recent-users">
                <?php if (empty($recentUsers)): ?>
                <div class="text-center py-4">
                    <i class="bi bi-people display-4 text-muted"></i>
                    <p class="text-muted mt-2 mb-0">No users yet</p>
                </div>
                <?php else: ?>
                <?php foreach ($recentUsers as $user): ?>
                <a href="user_view.php?id=<?= $user['id'] ?>" class="user-item">
                    <div class="user-avatar">
                        <?= strtoupper(substr($user['full_name'] ?? 'U', 0, 2)) ?>
                    </div>
                    <div class="user-info">
                        <div class="user-name"><?= htmlspecialchars($user['full_name']) ?></div>
                        <div class="user-email"><?= htmlspecialchars($user['email']) ?></div>
                    </div>
                    <div class="user-status <?= $user['is_active'] ? 'status-active' : 'status-inactive' ?>"></div>
                </a>
                <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <a href="dashboard_mobile.php" class="nav-item active">
                <i class="bi bi-house-fill nav-icon"></i>
                <span class="nav-label">Home</span>
            </a>
            <a href="users_modern.php" class="nav-item">
                <i class="bi bi-people-fill nav-icon"></i>
                <span class="nav-label">Users</span>
            </a>
            <a href="dashboard_weightloss.php" class="nav-item">
                <i class="bi bi-graph-up nav-icon"></i>
                <span class="nav-label">Analytics</span>
            </a>
            <a href="courses.php" class="nav-item">
                <i class="bi bi-play-circle-fill nav-icon"></i>
                <span class="nav-label">Courses</span>
            </a>
            <a href="logout.php" class="nav-item">
                <i class="bi bi-box-arrow-right nav-icon"></i>
                <span class="nav-label">Logout</span>
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Add active state to current nav item
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.nav-item');

            navItems.forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('href') && currentPath.includes(item.getAttribute('href'))) {
                    item.classList.add('active');
                }
            });
        });

        // Add touch feedback for mobile interactions
        document.querySelectorAll('.stat-card, .action-card, .user-item').forEach(element => {
            element.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.98)';
            });

            element.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>

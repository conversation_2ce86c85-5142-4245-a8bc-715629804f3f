<?php
/**
 * Video Watch Tracking Endpoint
 */

$user = authenticateRequest();
$database = new Database();
$db = $database->getConnection();

validateRequired($input, ['video_id', 'action']);

$videoId = (int)$input['video_id'];
$action = sanitizeInput($input['action']);
$currentTime = (int)($input['current_time'] ?? 0);
$totalTime = (int)($input['total_time'] ?? 0);

// Validate action
$validActions = ['start', 'pause', 'resume', 'complete'];
if (!in_array($action, $validActions)) {
    $response['message'] = 'Invalid action';
    http_response_code(400);
    echo json_encode($response);
    exit;
}

try {
    $db->beginTransaction();
    
    // Get current session
    $sessionToken = $_SERVER['HTTP_X_SESSION_TOKEN'] ?? null;
    $sessionId = null;
    
    if ($sessionToken) {
        $stmt = $db->prepare("
            SELECT id FROM user_sessions 
            WHERE user_id = ? AND session_token = ? AND ended_at IS NULL
            ORDER BY started_at DESC LIMIT 1
        ");
        $stmt->execute([$user['id'], $sessionToken]);
        $session = $stmt->fetch();
        $sessionId = $session['id'] ?? null;
    }
    
    // Log the activity
    $stmt = $db->prepare("
        INSERT INTO user_activity_log 
        (user_id, session_id, activity_type, video_id, duration_seconds, metadata)
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    
    $metadata = json_encode([
        'current_time' => $currentTime,
        'total_time' => $totalTime,
        'timestamp' => time()
    ]);
    
    $activityType = 'video_' . $action;
    $duration = ($action === 'complete') ? $totalTime : $currentTime;
    
    $stmt->execute([$user['id'], $sessionId, $activityType, $videoId, $duration, $metadata]);
    
    // Update session activity
    if ($sessionId) {
        $stmt = $db->prepare("
            UPDATE user_sessions 
            SET last_activity = NOW(),
                total_duration_seconds = total_duration_seconds + ?
            WHERE id = ?
        ");
        $stmt->execute([5, $sessionId]); // Add 5 seconds for each activity
    }
    
    // If video is completed, update progress
    if ($action === 'complete' && $totalTime > 0) {
        $completionPercentage = 100.0;
        
        $stmt = $db->prepare("
            INSERT INTO user_video_progress 
            (user_id, video_id, watch_time_seconds, completion_percentage, is_completed)
            VALUES (?, ?, ?, ?, 1)
            ON DUPLICATE KEY UPDATE
            watch_time_seconds = GREATEST(watch_time_seconds, VALUES(watch_time_seconds)),
            completion_percentage = 100.0,
            is_completed = 1,
            last_watched_at = NOW()
        ");
        
        $stmt->execute([$user['id'], $videoId, $totalTime, $completionPercentage]);
    }
    
    $db->commit();
    
    $response['success'] = true;
    $response['message'] = 'Activity logged successfully';
    
} catch (Exception $e) {
    $db->rollback();
    $response['message'] = 'Error logging activity: ' . $e->getMessage();
    http_response_code(500);
}

echo json_encode($response);
?>

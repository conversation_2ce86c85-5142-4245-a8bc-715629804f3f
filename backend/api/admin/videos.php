<?php
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../../includes/functions.php';

// Handle CORS
handleCORS();

// Check if admin is authenticated
session_start();
if (!isset($_SESSION['admin_id'])) {
    sendJsonResponse(['success' => false, 'message' => 'Unauthorized'], 401);
}

$database = new Database();
$db = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        handleGetVideos($db);
        break;
    case 'POST':
        handleCreateVideo($db);
        break;
    case 'PUT':
        handleUpdateVideo($db);
        break;
    case 'DELETE':
        handleDeleteVideo($db);
        break;
    default:
        sendJsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

function handleGetVideos($db) {
    try {
        $courseId = $_GET['course_id'] ?? null;
        $videoId = $_GET['id'] ?? null;
        
        if ($videoId) {
            // Get specific video
            $stmt = $db->prepare("
                SELECT cv.*, 
                       c.title as course_title,
                       COUNT(uvp.id) as view_count,
                       AVG(uvp.completion_percentage) as avg_completion
                FROM course_videos cv
                LEFT JOIN courses c ON cv.course_id = c.id
                LEFT JOIN user_video_progress uvp ON cv.id = uvp.video_id
                WHERE cv.id = ?
                GROUP BY cv.id
            ");
            $stmt->execute([$videoId]);
            $video = $stmt->fetch();
            
            if (!$video) {
                sendJsonResponse(['success' => false, 'message' => 'Video not found'], 404);
            }
            
            sendJsonResponse(['success' => true, 'data' => $video]);
        } elseif ($courseId) {
            // Get videos for specific course
            $stmt = $db->prepare("
                SELECT cv.*, 
                       COUNT(uvp.id) as view_count,
                       AVG(uvp.completion_percentage) as avg_completion
                FROM course_videos cv
                LEFT JOIN user_video_progress uvp ON cv.id = uvp.video_id
                WHERE cv.course_id = ?
                GROUP BY cv.id
                ORDER BY cv.order_index ASC, cv.unlock_day ASC
            ");
            $stmt->execute([$courseId]);
            $videos = $stmt->fetchAll();
            
            sendJsonResponse(['success' => true, 'data' => $videos]);
        } else {
            // Get all videos
            $stmt = $db->query("
                SELECT cv.*, 
                       c.title as course_title,
                       COUNT(uvp.id) as view_count,
                       AVG(uvp.completion_percentage) as avg_completion
                FROM course_videos cv
                LEFT JOIN courses c ON cv.course_id = c.id
                LEFT JOIN user_video_progress uvp ON cv.id = uvp.video_id
                GROUP BY cv.id
                ORDER BY c.title, cv.order_index ASC
            ");
            $videos = $stmt->fetchAll();
            
            sendJsonResponse(['success' => true, 'data' => $videos]);
        }
    } catch (Exception $e) {
        sendJsonResponse(['success' => false, 'message' => 'Error fetching videos: ' . $e->getMessage()], 500);
    }
}

function handleCreateVideo($db) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        validateRequired($input, ['course_id', 'title', 'vimeo_video_id']);
        
        $courseId = (int)$input['course_id'];
        $title = sanitizeInput($input['title']);
        $description = sanitizeInput($input['description'] ?? '');
        $vimeoVideoId = sanitizeInput($input['vimeo_video_id']);
        $unlockDay = (int)($input['unlock_day'] ?? 1);
        $orderIndex = (int)($input['order_index'] ?? 0);
        
        // Verify course exists
        $stmt = $db->prepare("SELECT id FROM courses WHERE id = ?");
        $stmt->execute([$courseId]);
        if (!$stmt->fetch()) {
            sendJsonResponse(['success' => false, 'message' => 'Course not found'], 404);
        }
        
        // Generate Vimeo embed URL
        $vimeoEmbedUrl = "https://player.vimeo.com/video/{$vimeoVideoId}";
        
        $stmt = $db->prepare("
            INSERT INTO course_videos (course_id, title, description, vimeo_video_id, vimeo_embed_url, unlock_day, order_index) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([$courseId, $title, $description, $vimeoVideoId, $vimeoEmbedUrl, $unlockDay, $orderIndex]);
        
        $videoId = $db->lastInsertId();
        
        // Get the created video
        $stmt = $db->prepare("
            SELECT cv.*, c.title as course_title
            FROM course_videos cv
            LEFT JOIN courses c ON cv.course_id = c.id
            WHERE cv.id = ?
        ");
        $stmt->execute([$videoId]);
        $video = $stmt->fetch();
        
        sendJsonResponse(['success' => true, 'message' => 'Video created successfully', 'data' => $video], 201);
        
    } catch (Exception $e) {
        sendJsonResponse(['success' => false, 'message' => 'Error creating video: ' . $e->getMessage()], 500);
    }
}

function handleUpdateVideo($db) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        validateRequired($input, ['id', 'title', 'vimeo_video_id']);
        
        $videoId = (int)$input['id'];
        $title = sanitizeInput($input['title']);
        $description = sanitizeInput($input['description'] ?? '');
        $vimeoVideoId = sanitizeInput($input['vimeo_video_id']);
        $unlockDay = (int)($input['unlock_day'] ?? 1);
        $orderIndex = (int)($input['order_index'] ?? 0);
        $isActive = isset($input['is_active']) ? (bool)$input['is_active'] : true;
        
        // Generate Vimeo embed URL
        $vimeoEmbedUrl = "https://player.vimeo.com/video/{$vimeoVideoId}";
        
        $stmt = $db->prepare("
            UPDATE course_videos 
            SET title = ?, description = ?, vimeo_video_id = ?, vimeo_embed_url = ?, 
                unlock_day = ?, order_index = ?, is_active = ?
            WHERE id = ?
        ");
        $stmt->execute([$title, $description, $vimeoVideoId, $vimeoEmbedUrl, $unlockDay, $orderIndex, $isActive, $videoId]);
        
        if ($stmt->rowCount() === 0) {
            sendJsonResponse(['success' => false, 'message' => 'Video not found or no changes made'], 404);
        }
        
        // Get the updated video
        $stmt = $db->prepare("
            SELECT cv.*, c.title as course_title
            FROM course_videos cv
            LEFT JOIN courses c ON cv.course_id = c.id
            WHERE cv.id = ?
        ");
        $stmt->execute([$videoId]);
        $video = $stmt->fetch();
        
        sendJsonResponse(['success' => true, 'message' => 'Video updated successfully', 'data' => $video]);
        
    } catch (Exception $e) {
        sendJsonResponse(['success' => false, 'message' => 'Error updating video: ' . $e->getMessage()], 500);
    }
}

function handleDeleteVideo($db) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        validateRequired($input, ['id']);
        
        $videoId = (int)$input['id'];
        
        // Check if video exists
        $stmt = $db->prepare("SELECT id FROM course_videos WHERE id = ?");
        $stmt->execute([$videoId]);
        if (!$stmt->fetch()) {
            sendJsonResponse(['success' => false, 'message' => 'Video not found'], 404);
        }
        
        // Delete video (cascade will handle related records)
        $stmt = $db->prepare("DELETE FROM course_videos WHERE id = ?");
        $stmt->execute([$videoId]);
        
        sendJsonResponse(['success' => true, 'message' => 'Video deleted successfully']);
        
    } catch (Exception $e) {
        sendJsonResponse(['success' => false, 'message' => 'Error deleting video: ' . $e->getMessage()], 500);
    }
}
?>

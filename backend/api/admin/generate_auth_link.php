<?php
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../includes/Auth.php';

// Handle CORS
handleCORS();

// Check if admin is authenticated
session_start();
if (!isset($_SESSION['admin_id'])) {
    sendJsonResponse(['success' => false, 'message' => 'Unauthorized'], 401);
}

$database = new Database();
$db = $database->getConnection();
$auth = new Auth();

$method = $_SERVER['REQUEST_METHOD'];

if ($method !== 'POST') {
    sendJsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    validateRequired($input, ['user_id']);
    
    $userId = (int)$input['user_id'];
    $adminId = $_SESSION['admin_id'];
    
    // Verify user exists
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ? AND is_active = 1");
    $stmt->execute([$userId]);
    $user = $stmt->fetch();
    
    if (!$user) {
        sendJsonResponse(['success' => false, 'message' => 'User not found'], 404);
    }
    
    // Generate temporary access token (1 hour expiry)
    $accessToken = generateUniqueToken(64);
    $expiresAt = gmdate('Y-m-d H:i:s', time() + 3600); // 1 hour expiry in UTC
    
    $stmt = $db->prepare("
        INSERT INTO auth_tokens (user_id, token, expires_at, created_by) 
        VALUES (?, ?, ?, ?)
    ");
    $stmt->execute([$userId, $accessToken, $expiresAt, $adminId]);
    
    // Generate the auth links
    $baseUrl = rtrim(APP_URL, '/');
    $webAuthLink = $baseUrl . '/?token=' . $accessToken;
    $phpAuthLink = $baseUrl . '/user/index.php?token=' . $user['unique_token'];
    
    // Log activity
    logActivity("Admin {$adminId} generated auth link for user {$userId}", 'INFO');
    
    sendJsonResponse([
        'success' => true,
        'message' => 'Auth link generated successfully',
        'data' => [
            'access_token' => $accessToken,
            'web_auth_link' => $webAuthLink,
            'php_auth_link' => $phpAuthLink,
            'permanent_token' => $user['unique_token'],
            'expires_at' => $expiresAt,
            'expires_in_seconds' => 3600,
            'user' => [
                'id' => $user['id'],
                'full_name' => $user['full_name'],
                'email' => $user['email'],
                'is_active' => $user['is_active']
            ]
        ]
    ]);
    
} catch (Exception $e) {
    logActivity("Auth link generation error: " . $e->getMessage(), 'ERROR');
    sendJsonResponse(['success' => false, 'message' => 'Error generating auth link: ' . $e->getMessage()], 500);
}
?>

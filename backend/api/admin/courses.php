<?php
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../../includes/functions.php';

// Handle CORS
handleCORS();

// Check if admin is authenticated
session_start();
if (!isset($_SESSION['admin_id'])) {
    sendJsonResponse(['success' => false, 'message' => 'Unauthorized'], 401);
}

$database = new Database();
$db = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        handleGetCourses($db);
        break;
    case 'POST':
        handleCreateCourse($db);
        break;
    case 'PUT':
        handleUpdateCourse($db);
        break;
    case 'DELETE':
        handleDeleteCourse($db);
        break;
    default:
        sendJsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

function handleGetCourses($db) {
    try {
        $courseId = $_GET['id'] ?? null;
        
        if ($courseId) {
            // Get specific course
            $stmt = $db->prepare("
                SELECT c.*, 
                       a.username as created_by_name,
                       COUNT(cv.id) as video_count,
                       COUNT(uc.id) as assigned_users
                FROM courses c
                LEFT JOIN admins a ON c.created_by = a.id
                LEFT JOIN course_videos cv ON c.id = cv.course_id AND cv.is_active = 1
                LEFT JOIN user_courses uc ON c.id = uc.course_id AND uc.is_active = 1
                WHERE c.id = ?
                GROUP BY c.id
            ");
            $stmt->execute([$courseId]);
            $course = $stmt->fetch();
            
            if (!$course) {
                sendJsonResponse(['success' => false, 'message' => 'Course not found'], 404);
            }
            
            sendJsonResponse(['success' => true, 'data' => $course]);
        } else {
            // Get all courses
            $stmt = $db->query("
                SELECT c.*, 
                       a.username as created_by_name,
                       COUNT(cv.id) as video_count,
                       COUNT(uc.id) as assigned_users
                FROM courses c
                LEFT JOIN admins a ON c.created_by = a.id
                LEFT JOIN course_videos cv ON c.id = cv.course_id AND cv.is_active = 1
                LEFT JOIN user_courses uc ON c.id = uc.course_id AND uc.is_active = 1
                GROUP BY c.id, c.title, c.description, c.duration_days, c.video_unlock_interval, 
                         c.is_active, c.created_by, c.created_at, c.updated_at, a.username
                ORDER BY c.created_at DESC
            ");
            $courses = $stmt->fetchAll();
            
            sendJsonResponse(['success' => true, 'data' => $courses]);
        }
    } catch (Exception $e) {
        sendJsonResponse(['success' => false, 'message' => 'Error fetching courses: ' . $e->getMessage()], 500);
    }
}

function handleCreateCourse($db) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        validateRequired($input, ['title']);
        
        $title = sanitizeInput($input['title']);
        $description = sanitizeInput($input['description'] ?? '');
        $durationDays = (int)($input['duration_days'] ?? 56);
        $videoUnlockInterval = (int)($input['video_unlock_interval'] ?? 8);
        
        $stmt = $db->prepare("
            INSERT INTO courses (title, description, duration_days, video_unlock_interval, created_by) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$title, $description, $durationDays, $videoUnlockInterval, $_SESSION['admin_id']]);
        
        $courseId = $db->lastInsertId();
        
        // Get the created course
        $stmt = $db->prepare("
            SELECT c.*, a.username as created_by_name
            FROM courses c
            LEFT JOIN admins a ON c.created_by = a.id
            WHERE c.id = ?
        ");
        $stmt->execute([$courseId]);
        $course = $stmt->fetch();
        
        sendJsonResponse(['success' => true, 'message' => 'Course created successfully', 'data' => $course], 201);
        
    } catch (Exception $e) {
        sendJsonResponse(['success' => false, 'message' => 'Error creating course: ' . $e->getMessage()], 500);
    }
}

function handleUpdateCourse($db) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        validateRequired($input, ['id', 'title']);
        
        $courseId = (int)$input['id'];
        $title = sanitizeInput($input['title']);
        $description = sanitizeInput($input['description'] ?? '');
        $durationDays = (int)($input['duration_days'] ?? 56);
        $videoUnlockInterval = (int)($input['video_unlock_interval'] ?? 8);
        $isActive = isset($input['is_active']) ? (bool)$input['is_active'] : true;
        
        $stmt = $db->prepare("
            UPDATE courses 
            SET title = ?, description = ?, duration_days = ?, video_unlock_interval = ?, is_active = ?
            WHERE id = ?
        ");
        $stmt->execute([$title, $description, $durationDays, $videoUnlockInterval, $isActive, $courseId]);
        
        if ($stmt->rowCount() === 0) {
            sendJsonResponse(['success' => false, 'message' => 'Course not found or no changes made'], 404);
        }
        
        // Get the updated course
        $stmt = $db->prepare("
            SELECT c.*, a.username as created_by_name
            FROM courses c
            LEFT JOIN admins a ON c.created_by = a.id
            WHERE c.id = ?
        ");
        $stmt->execute([$courseId]);
        $course = $stmt->fetch();
        
        sendJsonResponse(['success' => true, 'message' => 'Course updated successfully', 'data' => $course]);
        
    } catch (Exception $e) {
        sendJsonResponse(['success' => false, 'message' => 'Error updating course: ' . $e->getMessage()], 500);
    }
}

function handleDeleteCourse($db) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        validateRequired($input, ['id']);
        
        $courseId = (int)$input['id'];
        
        // Check if course exists
        $stmt = $db->prepare("SELECT id FROM courses WHERE id = ?");
        $stmt->execute([$courseId]);
        if (!$stmt->fetch()) {
            sendJsonResponse(['success' => false, 'message' => 'Course not found'], 404);
        }
        
        // Delete course (cascade will handle related records)
        $stmt = $db->prepare("DELETE FROM courses WHERE id = ?");
        $stmt->execute([$courseId]);
        
        sendJsonResponse(['success' => true, 'message' => 'Course deleted successfully']);
        
    } catch (Exception $e) {
        sendJsonResponse(['success' => false, 'message' => 'Error deleting course: ' . $e->getMessage()], 500);
    }
}
?>

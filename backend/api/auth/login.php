<?php
/**
 * User Login Endpoint
 */

require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../includes/Auth.php';
require_once __DIR__ . '/../../includes/JWT.php';

// Handle CORS
handleCORS();

$method = $_SERVER['REQUEST_METHOD'];

if ($method !== 'POST') {
    sendJsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

try {
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        sendJsonResponse(['success' => false, 'message' => 'Invalid JSON input'], 400);
    }

    validateRequired($input, ['token']);

$auth = new Auth();
$token = sanitizeInput($input['token']);

// Check if it's a one-time auth token
$userId = $auth->useAuthToken($token);

if ($userId) {
    // One-time token authentication - get user data directly
    $database = new Database();
    $db = $database->getConnection();
    
    $stmt = $db->prepare("
        SELECT u.*, uc.course_id 
        FROM users u 
        LEFT JOIN user_courses uc ON u.id = uc.user_id AND uc.is_active = 1
        WHERE u.id = ? AND u.is_active = 1
    ");
    $stmt->execute([$userId]);
    $user = $stmt->fetch();
    
    if ($user) {
        // Update last login
        $stmt = $db->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
        $stmt->execute([$userId]);
        
        // Generate JWT token
        $jwtToken = JWT::generateToken($userId, 'user');
        
        // Create session
        $deviceInfo = $input['device_info'] ?? null;
        $sessionToken = $auth->createSession($userId, $deviceInfo);
        
            sendJsonResponse([
                'success' => true,
                'message' => 'Login successful',
                'data' => [
                    'user' => $user,
                    'jwt_token' => $jwtToken,
                    'session_token' => $sessionToken
                ]
            ]);

            // Log activity
            logActivity("User {$userId} logged in successfully via auth link", 'INFO');
        } else {
            sendJsonResponse(['success' => false, 'message' => 'Invalid authentication token'], 401);
        }
    } else {
        // Try regular token authentication
        $result = $auth->authenticateWithToken($token);

        if ($result) {
            // Create session
            $deviceInfo = $input['device_info'] ?? null;
            $sessionToken = $auth->createSession($result['user']['id'], $deviceInfo);

            sendJsonResponse([
                'success' => true,
                'message' => 'Login successful',
                'data' => [
                    'user' => $result['user'],
                    'jwt_token' => $result['jwt_token'],
                    'session_token' => $sessionToken
                ]
            ]);

            // Log activity
            logActivity("User {$result['user']['id']} logged in successfully via unique token", 'INFO');
        } else {
            sendJsonResponse(['success' => false, 'message' => 'Invalid authentication token'], 401);
        }
    }

} catch (Exception $e) {
    logActivity("Login error: " . $e->getMessage(), 'ERROR');
    sendJsonResponse(['success' => false, 'message' => 'Authentication failed'], 500);
}
?>

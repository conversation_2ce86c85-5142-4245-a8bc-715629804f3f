<?php
/**
 * Admin Login Endpoint
 */

validateRequired($input, ['username', 'password']);

$auth = new Auth();
$username = sanitizeInput($input['username']);
$password = $input['password'];

$result = $auth->authenticateAdmin($username, $password);

if ($result) {
    $response['success'] = true;
    $response['message'] = 'Admin login successful';
    $response['data'] = [
        'admin' => [
            'id' => $result['admin']['id'],
            'username' => $result['admin']['username'],
            'email' => $result['admin']['email']
        ],
        'jwt_token' => $result['jwt_token']
    ];
    
    // Log activity
    logActivity("Admin {$result['admin']['username']} logged in successfully", 'INFO');
} else {
    $response['message'] = 'Invalid credentials';
    http_response_code(401);
}

echo json_encode($response);
?>

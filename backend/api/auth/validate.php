<?php
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../includes/Auth.php';
require_once __DIR__ . '/../../includes/JWT.php';

// Handle CORS
handleCORS();

$method = $_SERVER['REQUEST_METHOD'];

if ($method !== 'POST') {
    sendJsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

try {
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        sendJsonResponse(['success' => false, 'message' => 'Invalid JSON input'], 400);
    }

    // Get token from Authorization header or request body
    $token = null;
    $headers = getallheaders();

    if (isset($headers['Authorization'])) {
        $authHeader = $headers['Authorization'];
        if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            $token = $matches[1];
        }
    } elseif (isset($input['token'])) {
        $token = $input['token'];
    }

    if (!$token) {
        sendJsonResponse(['success' => false, 'message' => 'No token provided'], 401);
    }

    // Validate JWT token
    $payload = JWT::validateToken($token);

    if (!$payload) {
        sendJsonResponse(['success' => false, 'message' => 'Invalid or expired token'], 401);
    }

    // Get user data
    $database = new Database();
    $db = $database->getConnection();

    $stmt = $db->prepare("
        SELECT u.*, uc.course_id
        FROM users u
        LEFT JOIN user_courses uc ON u.id = uc.user_id AND uc.is_active = 1
        WHERE u.id = ? AND u.is_active = 1
    ");
    $stmt->execute([$payload['user_id']]);
    $user = $stmt->fetch();

    if (!$user) {
        sendJsonResponse(['success' => false, 'message' => 'User not found or inactive'], 401);
    }

    sendJsonResponse([
        'success' => true,
        'message' => 'Token is valid',
        'data' => [
            'user' => $user,
            'token_payload' => $payload
        ]
    ]);

} catch (Exception $e) {
    logActivity("Token validation error: " . $e->getMessage(), 'ERROR');
    sendJsonResponse(['success' => false, 'message' => 'Token validation failed'], 401);
}
?>

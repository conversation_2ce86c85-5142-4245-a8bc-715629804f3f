<?php
/**
 * User Progress Endpoint
 */

$user = authenticateRequest();
$database = new Database();
$db = $database->getConnection();

if ($method === 'GET') {
    // Get user progress summary
    try {
        // Overall progress
        $stmt = $db->prepare("
            SELECT 
                COUNT(DISTINCT uc.course_id) as total_courses,
                COUNT(DISTINCT cv.id) as total_videos,
                COUNT(DISTINCT uvp.video_id) as videos_started,
                COUNT(DISTINCT CASE WHEN uvp.is_completed = 1 THEN uvp.video_id END) as videos_completed,
                COALESCE(AVG(uvp.completion_percentage), 0) as avg_completion,
                COALESCE(SUM(uvp.watch_time_seconds), 0) as total_watch_time
            FROM user_courses uc
            JOIN courses c ON uc.course_id = c.id AND c.is_active = 1
            LEFT JOIN course_videos cv ON c.id = cv.course_id AND cv.is_active = 1
            LEFT JOIN user_video_progress uvp ON cv.id = uvp.video_id AND uvp.user_id = ?
            WHERE uc.user_id = ? AND uc.is_active = 1
        ");
        
        $stmt->execute([$user['id'], $user['id']]);
        $overall = $stmt->fetch();
        
        // Recent activity
        $stmt = $db->prepare("
            SELECT 
                ual.activity_type,
                ual.duration_seconds,
                ual.created_at,
                cv.title as video_title,
                c.title as course_title
            FROM user_activity_log ual
            LEFT JOIN course_videos cv ON ual.video_id = cv.id
            LEFT JOIN courses c ON cv.course_id = c.id
            WHERE ual.user_id = ?
            ORDER BY ual.created_at DESC
            LIMIT 20
        ");
        
        $stmt->execute([$user['id']]);
        $recent_activity = $stmt->fetchAll();
        
        // Weekly progress
        $stmt = $db->prepare("
            SELECT 
                DATE(ual.created_at) as date,
                COUNT(*) as activities,
                COALESCE(SUM(ual.duration_seconds), 0) as total_seconds
            FROM user_activity_log ual
            WHERE ual.user_id = ? 
                AND ual.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                AND ual.activity_type IN ('video_start', 'video_complete')
            GROUP BY DATE(ual.created_at)
            ORDER BY date DESC
        ");
        
        $stmt->execute([$user['id']]);
        $weekly_progress = $stmt->fetchAll();
        
        $response['success'] = true;
        $response['message'] = 'Progress retrieved successfully';
        $response['data'] = [
            'overall' => $overall,
            'recent_activity' => $recent_activity,
            'weekly_progress' => $weekly_progress
        ];
        
    } catch (Exception $e) {
        $response['message'] = 'Error retrieving progress: ' . $e->getMessage();
        http_response_code(500);
    }
    
} elseif ($method === 'POST') {
    // Update video progress
    validateRequired($input, ['video_id', 'watch_time_seconds', 'completion_percentage']);
    
    $videoId = (int)$input['video_id'];
    $watchTimeSeconds = (int)$input['watch_time_seconds'];
    $completionPercentage = (float)$input['completion_percentage'];
    $isCompleted = $completionPercentage >= 90 ? 1 : 0;
    
    try {
        $db->beginTransaction();
        
        // Update or insert video progress
        $stmt = $db->prepare("
            INSERT INTO user_video_progress 
            (user_id, video_id, watch_time_seconds, completion_percentage, is_completed)
            VALUES (?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            watch_time_seconds = GREATEST(watch_time_seconds, VALUES(watch_time_seconds)),
            completion_percentage = GREATEST(completion_percentage, VALUES(completion_percentage)),
            is_completed = VALUES(is_completed),
            last_watched_at = NOW()
        ");
        
        $stmt->execute([$user['id'], $videoId, $watchTimeSeconds, $completionPercentage, $isCompleted]);
        
        // Log activity
        $activityType = $isCompleted ? 'video_complete' : 'video_progress';
        $stmt = $db->prepare("
            INSERT INTO user_activity_log 
            (user_id, activity_type, video_id, duration_seconds)
            VALUES (?, ?, ?, ?)
        ");
        
        $stmt->execute([$user['id'], $activityType, $videoId, $watchTimeSeconds]);
        
        $db->commit();
        
        $response['success'] = true;
        $response['message'] = 'Progress updated successfully';
        
    } catch (Exception $e) {
        $db->rollback();
        $response['message'] = 'Error updating progress: ' . $e->getMessage();
        http_response_code(500);
    }
}

echo json_encode($response);
?>

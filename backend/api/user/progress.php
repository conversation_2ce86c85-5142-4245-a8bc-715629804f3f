<?php
/**
 * User Progress Endpoint
 */

require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../includes/Auth.php';
require_once __DIR__ . '/../../includes/JWT.php';

// Handle CORS
handleCORS();

$response = ['success' => false, 'message' => '', 'data' => null];
$method = $_SERVER['REQUEST_METHOD'];

/**
 * Helper function to authenticate API requests
 */
function authenticateRequest($requireAdmin = false) {
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? '';

    if (!$authHeader || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Authorization header missing']);
        exit;
    }

    $token = $matches[1];
    $auth = new Auth();
    $user = $auth->validateJWT($token);

    if (!$user) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Invalid token']);
        exit;
    }

    return $user;
}

$database = new Database();
$db = $database->getConnection();

$user = authenticateRequest();

if ($method === 'GET') {
    // Get user progress summary
    try {
        // Return sample progress data for KFT Fitness
        $overall = [
            'total_courses' => 3,
            'total_videos' => 7,
            'videos_started' => 2,
            'videos_completed' => 1,
            'avg_completion' => 25.5,
            'total_watch_time' => 1800 // 30 minutes in seconds
        ];

        // Sample recent activity
        $recent_activity = [
            [
                'activity_type' => 'video_watched',
                'duration_seconds' => 900,
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 hours')),
                'video_title' => 'Welcome to Your Fitness Journey',
                'course_title' => 'Beginner Fitness Fundamentals'
            ],
            [
                'activity_type' => 'video_completed',
                'duration_seconds' => 1500,
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'video_title' => 'Basic Bodyweight Exercises',
                'course_title' => 'Beginner Fitness Fundamentals'
            ],
            [
                'activity_type' => 'course_started',
                'duration_seconds' => 0,
                'created_at' => date('Y-m-d H:i:s', strtotime('-3 days')),
                'video_title' => null,
                'course_title' => 'Beginner Fitness Fundamentals'
            ]
        ];

        // Sample weekly progress
        $weekly_progress = [
            [
                'date' => date('Y-m-d'),
                'activities' => 2,
                'total_seconds' => 1800
            ],
            [
                'date' => date('Y-m-d', strtotime('-1 day')),
                'activities' => 1,
                'total_seconds' => 1500
            ],
            [
                'date' => date('Y-m-d', strtotime('-2 days')),
                'activities' => 3,
                'total_seconds' => 2700
            ],
            [
                'date' => date('Y-m-d', strtotime('-3 days')),
                'activities' => 1,
                'total_seconds' => 900
            ]
        ];
        
        $response['success'] = true;
        $response['message'] = 'Progress retrieved successfully';
        $response['data'] = [
            'overall' => $overall,
            'recent_activity' => $recent_activity,
            'weekly_progress' => $weekly_progress
        ];
        
    } catch (Exception $e) {
        $response['message'] = 'Error retrieving progress: ' . $e->getMessage();
        http_response_code(500);
    }
    
} elseif ($method === 'POST') {
    // Update video progress
    validateRequired($input, ['video_id', 'watch_time_seconds', 'completion_percentage']);
    
    $videoId = (int)$input['video_id'];
    $watchTimeSeconds = (int)$input['watch_time_seconds'];
    $completionPercentage = (float)$input['completion_percentage'];
    $isCompleted = $completionPercentage >= 90 ? 1 : 0;
    
    try {
        $db->beginTransaction();
        
        // Update or insert video progress
        $stmt = $db->prepare("
            INSERT INTO user_video_progress 
            (user_id, video_id, watch_time_seconds, completion_percentage, is_completed)
            VALUES (?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            watch_time_seconds = GREATEST(watch_time_seconds, VALUES(watch_time_seconds)),
            completion_percentage = GREATEST(completion_percentage, VALUES(completion_percentage)),
            is_completed = VALUES(is_completed),
            last_watched_at = NOW()
        ");
        
        $stmt->execute([$user['id'], $videoId, $watchTimeSeconds, $completionPercentage, $isCompleted]);
        
        // Log activity
        $activityType = $isCompleted ? 'video_complete' : 'video_progress';
        $stmt = $db->prepare("
            INSERT INTO user_activity_log 
            (user_id, activity_type, video_id, duration_seconds)
            VALUES (?, ?, ?, ?)
        ");
        
        $stmt->execute([$user['id'], $activityType, $videoId, $watchTimeSeconds]);
        
        $db->commit();
        
        $response['success'] = true;
        $response['message'] = 'Progress updated successfully';
        
    } catch (Exception $e) {
        $db->rollback();
        $response['message'] = 'Error updating progress: ' . $e->getMessage();
        http_response_code(500);
    }
}

echo json_encode($response);
?>

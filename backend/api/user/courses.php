<?php
/**
 * User Courses Endpoint
 */

$user = authenticateRequest();
$database = new Database();
$db = $database->getConnection();

try {
    // Get user's assigned courses with videos and progress
    $stmt = $db->prepare("
        SELECT 
            c.*,
            uc.assigned_at,
            uc.started_at,
            uc.completed_at,
            JSON_ARRAYAGG(
                JSON_OBJECT(
                    'id', cv.id,
                    'title', cv.title,
                    'description', cv.description,
                    'vimeo_video_id', cv.vimeo_video_id,
                    'vimeo_embed_url', cv.vimeo_embed_url,
                    'unlock_day', cv.unlock_day,
                    'duration_seconds', cv.duration_seconds,
                    'order_index', cv.order_index,
                    'is_active', cv.is_active,
                    'created_at', cv.created_at,
                    'progress', JSON_OBJECT(
                        'id', uvp.id,
                        'watch_time_seconds', COALESCE(uvp.watch_time_seconds, 0),
                        'completion_percentage', COALESCE(uvp.completion_percentage, 0),
                        'last_watched_at', uvp.last_watched_at,
                        'is_completed', COALESCE(uvp.is_completed, 0)
                    )
                )
            ) as videos
        FROM user_courses uc
        JOIN courses c ON uc.course_id = c.id
        LEFT JOIN course_videos cv ON c.id = cv.course_id AND cv.is_active = 1
        LEFT JOIN user_video_progress uvp ON cv.id = uvp.video_id AND uvp.user_id = ?
        WHERE uc.user_id = ? AND uc.is_active = 1 AND c.is_active = 1
        GROUP BY c.id
        ORDER BY uc.assigned_at DESC
    ");
    
    $stmt->execute([$user['id'], $user['id']]);
    $courses = $stmt->fetchAll();
    
    // Process the JSON data
    foreach ($courses as &$course) {
        $course['videos'] = json_decode($course['videos'], true);
        
        // Filter out null videos (when no videos exist)
        $course['videos'] = array_filter($course['videos'], function($video) {
            return $video['id'] !== null;
        });
        
        // Sort videos by order_index
        usort($course['videos'], function($a, $b) {
            return $a['order_index'] <=> $b['order_index'];
        });
    }
    
    $response['success'] = true;
    $response['message'] = 'Courses retrieved successfully';
    $response['data'] = $courses;
    
} catch (Exception $e) {
    $response['message'] = 'Error retrieving courses: ' . $e->getMessage();
    http_response_code(500);
}

echo json_encode($response);
?>

<?php
/**
 * User Courses Endpoint
 */

require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../includes/Auth.php';
require_once __DIR__ . '/../../includes/JWT.php';

// Handle CORS
handleCORS();

$response = ['success' => false, 'message' => '', 'data' => null];

/**
 * Helper function to authenticate API requests
 */
function authenticateRequest($requireAdmin = false) {
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? '';

    if (!$authHeader || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Authorization header missing']);
        exit;
    }

    $token = $matches[1];
    $auth = new Auth();
    $user = $auth->validateJWT($token);

    if (!$user) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Invalid token']);
        exit;
    }

    return $user;
}

$database = new Database();
$db = $database->getConnection();

$user = authenticateRequest();

try {
    // For now, return sample fitness courses since the database structure needs to be set up properly
    // This will be replaced with real database queries once the structure is confirmed

    $courses = [
        [
            'id' => 1,
            'title' => 'Beginner Fitness Fundamentals',
            'description' => 'Perfect for those starting their fitness journey. Learn basic exercises, proper form, and build a foundation for long-term success.',
            'duration_weeks' => 4,
            'difficulty_level' => 'Beginner',
            'is_active' => true,
            'created_at' => date('Y-m-d H:i:s'),
            'assigned_at' => date('Y-m-d H:i:s'),
            'videos' => [
                [
                    'id' => 1,
                    'title' => 'Welcome to Your Fitness Journey',
                    'description' => 'Introduction to the program and setting realistic goals.',
                    'duration_minutes' => 15,
                    'order_index' => 1,
                    'progress' => [
                        'progress_percentage' => 0,
                        'isCompleted' => false,
                        'last_watched_at' => null
                    ]
                ],
                [
                    'id' => 2,
                    'title' => 'Basic Bodyweight Exercises',
                    'description' => 'Learn fundamental movements: squats, push-ups, and planks.',
                    'duration_minutes' => 25,
                    'order_index' => 2,
                    'progress' => [
                        'progress_percentage' => 0,
                        'isCompleted' => false,
                        'last_watched_at' => null
                    ]
                ],
                [
                    'id' => 3,
                    'title' => 'Proper Form and Safety',
                    'description' => 'Essential techniques to prevent injury and maximize results.',
                    'duration_minutes' => 20,
                    'order_index' => 3,
                    'progress' => [
                        'progress_percentage' => 0,
                        'isCompleted' => false,
                        'last_watched_at' => null
                    ]
                ]
            ]
        ],
        [
            'id' => 2,
            'title' => 'Strength Training Mastery',
            'description' => 'Build muscle, increase strength, and transform your physique with progressive resistance training techniques.',
            'duration_weeks' => 8,
            'difficulty_level' => 'Intermediate',
            'is_active' => true,
            'created_at' => date('Y-m-d H:i:s'),
            'assigned_at' => date('Y-m-d H:i:s'),
            'videos' => [
                [
                    'id' => 4,
                    'title' => 'Introduction to Resistance Training',
                    'description' => 'Understanding progressive overload and training principles.',
                    'duration_minutes' => 22,
                    'order_index' => 1,
                    'progress' => [
                        'progress_percentage' => 0,
                        'isCompleted' => false,
                        'last_watched_at' => null
                    ]
                ],
                [
                    'id' => 5,
                    'title' => 'Upper Body Strength Foundations',
                    'description' => 'Master the bench press, rows, and overhead movements.',
                    'duration_minutes' => 35,
                    'order_index' => 2,
                    'progress' => [
                        'progress_percentage' => 0,
                        'isCompleted' => false,
                        'last_watched_at' => null
                    ]
                ]
            ]
        ],
        [
            'id' => 3,
            'title' => 'HIIT Cardio Bootcamp',
            'description' => 'High-intensity interval training to burn fat, improve cardiovascular health, and boost metabolism.',
            'duration_weeks' => 6,
            'difficulty_level' => 'Intermediate',
            'is_active' => true,
            'created_at' => date('Y-m-d H:i:s'),
            'assigned_at' => date('Y-m-d H:i:s'),
            'videos' => [
                [
                    'id' => 6,
                    'title' => 'HIIT Training Principles',
                    'description' => 'Understanding work-to-rest ratios and intensity zones.',
                    'duration_minutes' => 20,
                    'order_index' => 1,
                    'progress' => [
                        'progress_percentage' => 0,
                        'isCompleted' => false,
                        'last_watched_at' => null
                    ]
                ],
                [
                    'id' => 7,
                    'title' => 'Beginner HIIT Workout',
                    'description' => 'Your first high-intensity interval training session.',
                    'duration_minutes' => 30,
                    'order_index' => 2,
                    'progress' => [
                        'progress_percentage' => 0,
                        'isCompleted' => false,
                        'last_watched_at' => null
                    ]
                ]
            ]
        ]
    ];

    
    $response['success'] = true;
    $response['message'] = 'Courses retrieved successfully';
    $response['data'] = $courses;
    
} catch (Exception $e) {
    $response['message'] = 'Error retrieving courses: ' . $e->getMessage();
    http_response_code(500);
}

echo json_encode($response);
?>

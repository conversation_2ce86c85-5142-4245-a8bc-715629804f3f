<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/Auth.php';

// Handle CORS
handleCORS();

// Set JSON content type
header('Content-Type: application/json');

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$path = str_replace('/api', '', $path);

// Get request body for POST/PUT requests
$input = json_decode(file_get_contents('php://input'), true);

// Initialize response
$response = ['success' => false, 'message' => '', 'data' => null];

try {
    // Route handling
    switch ($path) {
        case '/auth/login':
            if ($method === 'POST') {
                require_once __DIR__ . '/auth/login.php';
            }
            break;
            
        case '/auth/admin-login':
            if ($method === 'POST') {
                require_once __DIR__ . '/auth/admin-login.php';
            }
            break;
            
        case '/auth/validate':
            if ($method === 'POST') {
                require_once __DIR__ . '/auth/validate.php';
            }
            break;
            
        case '/user/profile':
            if ($method === 'GET') {
                require_once __DIR__ . '/user/profile.php';
            }
            break;
            
        case '/user/courses':
            if ($method === 'GET') {
                require_once __DIR__ . '/user/courses.php';
            }
            break;
            
        case '/user/progress':
            if ($method === 'GET' || $method === 'POST') {
                require_once __DIR__ . '/user/progress.php';
            }
            break;
            
        case '/videos/watch':
            if ($method === 'POST') {
                require_once __DIR__ . '/videos/watch.php';
            }
            break;
            
        case '/admin/users':
            if ($method === 'GET' || $method === 'POST') {
                require_once __DIR__ . '/admin/users.php';
            }
            break;
            
        case '/admin/courses':
            if ($method === 'GET' || $method === 'POST') {
                require_once __DIR__ . '/admin/courses.php';
            }
            break;
            
        case '/admin/analytics':
            if ($method === 'GET') {
                require_once __DIR__ . '/admin/analytics.php';
            }
            break;

        case '/admin/generate_auth_link':
            if ($method === 'POST') {
                require_once __DIR__ . '/admin/generate_auth_link.php';
            }
            break;
            
        default:
            if (preg_match('/^\/admin\/users\/(\d+)/', $path, $matches)) {
                $userId = $matches[1];
                if ($method === 'GET' || $method === 'PUT' || $method === 'DELETE') {
                    require_once __DIR__ . '/admin/user-detail.php';
                }
            } elseif (preg_match('/^\/admin\/courses\/(\d+)/', $path, $matches)) {
                $courseId = $matches[1];
                if ($method === 'GET' || $method === 'PUT' || $method === 'DELETE') {
                    require_once __DIR__ . '/admin/course-detail.php';
                }
            } elseif (preg_match('/^\/videos\/(\d+)/', $path, $matches)) {
                $videoId = $matches[1];
                if ($method === 'GET') {
                    require_once __DIR__ . '/videos/video-detail.php';
                }
            } else {
                $response['message'] = 'Endpoint not found';
                http_response_code(404);
            }
            break;
    }
    
} catch (Exception $e) {
    $response['message'] = 'Internal server error';
    logActivity("API Error: " . $e->getMessage(), 'ERROR');
    http_response_code(500);
}

// Send response if not already sent
if (!headers_sent()) {
    echo json_encode($response);
}

/**
 * Helper function to authenticate API requests
 */
function authenticateRequest($requireAdmin = false) {
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? '';
    
    if (!$authHeader || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Authorization header missing']);
        exit;
    }
    
    $token = $matches[1];
    $auth = new Auth();
    $user = $auth->validateJWT($token);
    
    if (!$user) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Invalid token']);
        exit;
    }
    
    // Check if admin access is required
    if ($requireAdmin) {
        $payload = JWT::validateToken($token);
        if (!$payload || $payload['user_type'] !== 'admin') {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Admin access required']);
            exit;
        }
    }
    
    return $user;
}

/**
 * Helper function to validate required fields
 */
function validateRequired($data, $fields) {
    $missing = [];
    foreach ($fields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            $missing[] = $field;
        }
    }
    
    if (!empty($missing)) {
        http_response_code(400);
        echo json_encode([
            'success' => false, 
            'message' => 'Missing required fields: ' . implode(', ', $missing)
        ]);
        exit;
    }
}
?>

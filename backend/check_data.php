<?php
require_once __DIR__ . '/config/database.php';

$database = new Database();
$db = $database->getConnection();

echo "=== TABLES ===\n";
$stmt = $db->query('SHOW TABLES');
while ($row = $stmt->fetch()) {
    echo "Table: " . $row[0] . "\n";
}

echo "\n=== USERS ===\n";
$stmt = $db->query('SELECT id, full_name, email, is_active FROM users ORDER BY id');
while ($row = $stmt->fetch()) {
    echo "ID: {$row['id']}, Name: {$row['full_name']}, Email: {$row['email']}, Active: {$row['is_active']}\n";
}

// Check if courses table exists
try {
    echo "\n=== COURSES ===\n";
    $stmt = $db->query('SELECT * FROM courses ORDER BY id');
    while ($row = $stmt->fetch()) {
        echo "ID: {$row['id']}, Title: {$row['title']}, Description: {$row['description']}\n";
    }
} catch (Exception $e) {
    echo "Courses table doesn't exist\n";
}

// Check if videos table exists
try {
    echo "\n=== VIDEOS ===\n";
    $stmt = $db->query('SELECT * FROM videos ORDER BY course_id, order_index');
    while ($row = $stmt->fetch()) {
        echo "ID: {$row['id']}, Course: {$row['course_id']}, Title: {$row['title']}, Order: {$row['order_index']}\n";
    }
} catch (Exception $e) {
    echo "Videos table doesn't exist\n";
}
?>

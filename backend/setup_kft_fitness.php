<?php
require_once __DIR__ . '/config/database.php';

$database = new Database();
$db = $database->getConnection();

echo "<h1>Setting up KFT Fitness Database</h1>";

// Create courses table
$sql = "CREATE TABLE IF NOT EXISTS courses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    thumbnail VARCHAR(255),
    duration_weeks INT DEFAULT 0,
    difficulty_level ENUM('Beginner', 'Intermediate', 'Advanced') DEFAULT 'Beginner',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($db->exec($sql)) {
    echo "<p>✅ Courses table created successfully</p>";
} else {
    echo "<p>❌ Error creating courses table</p>";
}

// Create videos table
$sql = "CREATE TABLE IF NOT EXISTS videos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    video_url VARCHAR(500),
    thumbnail VARCHAR(255),
    duration_minutes INT DEFAULT 0,
    order_index INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
)";

if ($db->exec($sql)) {
    echo "<p>✅ Videos table created successfully</p>";
} else {
    echo "<p>❌ Error creating videos table</p>";
}

// Create user_courses table
$sql = "CREATE TABLE IF NOT EXISTS user_courses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_course (user_id, course_id)
)";

if ($db->exec($sql)) {
    echo "<p>✅ User courses table created successfully</p>";
} else {
    echo "<p>❌ Error creating user courses table</p>";
}

// Create user_progress table
$sql = "CREATE TABLE IF NOT EXISTS user_progress (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    video_id INT NOT NULL,
    watched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    progress_percentage INT DEFAULT 0,
    completed BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_video (user_id, video_id)
)";

if ($db->exec($sql)) {
    echo "<p>✅ User progress table created successfully</p>";
} else {
    echo "<p>❌ Error creating user progress table</p>";
}

// Insert sample fitness courses
$courses = [
    [
        'title' => 'Beginner Fitness Fundamentals',
        'description' => 'Perfect for those starting their fitness journey. Learn basic exercises, proper form, and build a foundation for long-term success.',
        'duration_weeks' => 4,
        'difficulty_level' => 'Beginner'
    ],
    [
        'title' => 'Strength Training Mastery',
        'description' => 'Build muscle, increase strength, and transform your physique with progressive resistance training techniques.',
        'duration_weeks' => 8,
        'difficulty_level' => 'Intermediate'
    ],
    [
        'title' => 'HIIT Cardio Bootcamp',
        'description' => 'High-intensity interval training to burn fat, improve cardiovascular health, and boost metabolism.',
        'duration_weeks' => 6,
        'difficulty_level' => 'Intermediate'
    ],
    [
        'title' => 'Flexibility & Mobility',
        'description' => 'Improve your range of motion, reduce injury risk, and enhance overall movement quality.',
        'duration_weeks' => 4,
        'difficulty_level' => 'Beginner'
    ],
    [
        'title' => 'Advanced Athletic Performance',
        'description' => 'Elite training protocols for serious athletes looking to maximize their performance potential.',
        'duration_weeks' => 12,
        'difficulty_level' => 'Advanced'
    ]
];

foreach ($courses as $course) {
    $stmt = $db->prepare("INSERT INTO courses (title, description, duration_weeks, difficulty_level) VALUES (?, ?, ?, ?)");
    $stmt->execute([$course['title'], $course['description'], $course['duration_weeks'], $course['difficulty_level']]);
}

echo "<p>✅ Sample courses inserted successfully</p>";

// Insert sample videos for each course
$videos = [
    // Beginner Fitness Fundamentals (Course ID: 1)
    [
        'course_id' => 1,
        'title' => 'Welcome to Your Fitness Journey',
        'description' => 'Introduction to the program and setting realistic goals.',
        'duration_minutes' => 15,
        'order_index' => 1
    ],
    [
        'course_id' => 1,
        'title' => 'Basic Bodyweight Exercises',
        'description' => 'Learn fundamental movements: squats, push-ups, and planks.',
        'duration_minutes' => 25,
        'order_index' => 2
    ],
    [
        'course_id' => 1,
        'title' => 'Proper Form and Safety',
        'description' => 'Essential techniques to prevent injury and maximize results.',
        'duration_minutes' => 20,
        'order_index' => 3
    ],
    [
        'course_id' => 1,
        'title' => 'Creating Your Workout Schedule',
        'description' => 'How to plan and structure your weekly training routine.',
        'duration_minutes' => 18,
        'order_index' => 4
    ],
    
    // Strength Training Mastery (Course ID: 2)
    [
        'course_id' => 2,
        'title' => 'Introduction to Resistance Training',
        'description' => 'Understanding progressive overload and training principles.',
        'duration_minutes' => 22,
        'order_index' => 1
    ],
    [
        'course_id' => 2,
        'title' => 'Upper Body Strength Foundations',
        'description' => 'Master the bench press, rows, and overhead movements.',
        'duration_minutes' => 35,
        'order_index' => 2
    ],
    [
        'course_id' => 2,
        'title' => 'Lower Body Power Development',
        'description' => 'Squats, deadlifts, and unilateral leg exercises.',
        'duration_minutes' => 40,
        'order_index' => 3
    ],
    [
        'course_id' => 2,
        'title' => 'Core Stability and Strength',
        'description' => 'Build a strong foundation with advanced core training.',
        'duration_minutes' => 28,
        'order_index' => 4
    ],
    
    // HIIT Cardio Bootcamp (Course ID: 3)
    [
        'course_id' => 3,
        'title' => 'HIIT Training Principles',
        'description' => 'Understanding work-to-rest ratios and intensity zones.',
        'duration_minutes' => 20,
        'order_index' => 1
    ],
    [
        'course_id' => 3,
        'title' => 'Beginner HIIT Workout',
        'description' => 'Your first high-intensity interval training session.',
        'duration_minutes' => 30,
        'order_index' => 2
    ],
    [
        'course_id' => 3,
        'title' => 'Intermediate Fat Burning Circuit',
        'description' => 'Step up the intensity with compound movements.',
        'duration_minutes' => 35,
        'order_index' => 3
    ],
    [
        'course_id' => 3,
        'title' => 'Advanced Metabolic Conditioning',
        'description' => 'Maximum intensity training for experienced athletes.',
        'duration_minutes' => 45,
        'order_index' => 4
    ],
    
    // Flexibility & Mobility (Course ID: 4)
    [
        'course_id' => 4,
        'title' => 'Mobility Assessment',
        'description' => 'Identify your movement limitations and imbalances.',
        'duration_minutes' => 25,
        'order_index' => 1
    ],
    [
        'course_id' => 4,
        'title' => 'Dynamic Warm-up Routine',
        'description' => 'Prepare your body for exercise with movement-based stretches.',
        'duration_minutes' => 15,
        'order_index' => 2
    ],
    [
        'course_id' => 4,
        'title' => 'Static Stretching for Recovery',
        'description' => 'Post-workout stretches to improve flexibility.',
        'duration_minutes' => 20,
        'order_index' => 3
    ],
    [
        'course_id' => 4,
        'title' => 'Foam Rolling and Self-Massage',
        'description' => 'Techniques for muscle recovery and tension release.',
        'duration_minutes' => 18,
        'order_index' => 4
    ],
    
    // Advanced Athletic Performance (Course ID: 5)
    [
        'course_id' => 5,
        'title' => 'Periodization and Programming',
        'description' => 'Advanced training cycles for peak performance.',
        'duration_minutes' => 30,
        'order_index' => 1
    ],
    [
        'course_id' => 5,
        'title' => 'Olympic Lifting Techniques',
        'description' => 'Master the clean, jerk, and snatch movements.',
        'duration_minutes' => 50,
        'order_index' => 2
    ],
    [
        'course_id' => 5,
        'title' => 'Plyometric Power Training',
        'description' => 'Explosive movements for athletic performance.',
        'duration_minutes' => 40,
        'order_index' => 3
    ],
    [
        'course_id' => 5,
        'title' => 'Sport-Specific Conditioning',
        'description' => 'Tailor your training to your specific sport demands.',
        'duration_minutes' => 45,
        'order_index' => 4
    ]
];

foreach ($videos as $video) {
    $stmt = $db->prepare("INSERT INTO videos (course_id, title, description, duration_minutes, order_index) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute([$video['course_id'], $video['title'], $video['description'], $video['duration_minutes'], $video['order_index']]);
}

echo "<p>✅ Sample videos inserted successfully</p>";

// Enroll existing users in some courses
$stmt = $db->query("SELECT id FROM users WHERE is_active = 1");
$users = $stmt->fetchAll();

foreach ($users as $user) {
    // Enroll each user in 2-3 random courses
    $courseIds = [1, 2, 3, 4, 5];
    shuffle($courseIds);
    $enrolledCourses = array_slice($courseIds, 0, rand(2, 3));
    
    foreach ($enrolledCourses as $courseId) {
        $stmt = $db->prepare("INSERT IGNORE INTO user_courses (user_id, course_id) VALUES (?, ?)");
        $stmt->execute([$user['id'], $courseId]);
    }
}

echo "<p>✅ Users enrolled in courses successfully</p>";

echo "<h2>Database Setup Complete!</h2>";
echo "<p>KFT Fitness database has been set up with:</p>";
echo "<ul>";
echo "<li>5 Fitness courses with different difficulty levels</li>";
echo "<li>20 Training videos across all courses</li>";
echo "<li>User enrollment system</li>";
echo "<li>Progress tracking capabilities</li>";
echo "</ul>";

echo "<p><a href='check_data.php'>View Database Contents</a></p>";
?>

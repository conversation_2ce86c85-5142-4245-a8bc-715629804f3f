<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

// Get user token and course ID from URL
$token = $_GET['token'] ?? '';
$courseId = (int)($_GET['course_id'] ?? 0);

if (empty($token) || !$courseId) {
    die('Access token and course ID required');
}

$database = new Database();
$db = $database->getConnection();

// Validate token and get user
$stmt = $db->prepare("SELECT * FROM users WHERE unique_token = ? AND is_active = 1");
$stmt->execute([$token]);
$user = $stmt->fetch();

if (!$user) {
    die('Invalid or expired token');
}

// Verify user has access to this course
$stmt = $db->prepare("
    SELECT c.*, uc.assigned_at, uc.started_at, uc.completed_at
    FROM courses c
    JOIN user_courses uc ON c.id = uc.course_id
    WHERE c.id = ? AND uc.user_id = ? AND uc.is_active = 1 AND c.is_active = 1
    LIMIT 1
");
$stmt->execute([$courseId, $user['id']]);
$course = $stmt->fetch();

if (!$course) {
    die('Course not found or access denied');
}

// Get course videos with progress
$stmt = $db->prepare("
    SELECT cv.*, 
           uvp.watch_time_seconds,
           uvp.completion_percentage,
           uvp.last_watched_at,
           uvp.is_completed,
           CASE 
               WHEN uc.started_at IS NULL THEN 0
               WHEN DATE_ADD(uc.started_at, INTERVAL (cv.unlock_day - 1) DAY) <= NOW() THEN 1
               ELSE 0
           END as is_unlocked,
           DATE_ADD(uc.started_at, INTERVAL (cv.unlock_day - 1) DAY) as unlock_date
    FROM course_videos cv
    LEFT JOIN user_video_progress uvp ON cv.id = uvp.video_id AND uvp.user_id = ?
    LEFT JOIN user_courses uc ON cv.course_id = uc.course_id AND uc.user_id = ?
    WHERE cv.course_id = ? AND cv.is_active = 1
    ORDER BY cv.order_index ASC, cv.unlock_day ASC
");
$stmt->execute([$user['id'], $user['id'], $courseId]);
$videos = $stmt->fetchAll();

// Start course if not started
if (!$course['started_at']) {
    $stmt = $db->prepare("UPDATE user_courses SET started_at = NOW() WHERE user_id = ? AND course_id = ?");
    $stmt->execute([$user['id'], $courseId]);
    $course['started_at'] = date('Y-m-d H:i:s');
}

// Calculate progress
$totalVideos = count($videos);
$completedVideos = 0;
$unlockedVideos = 0;

foreach ($videos as $video) {
    if ($video['is_completed']) $completedVideos++;
    if ($video['is_unlocked']) $unlockedVideos++;
}

$progress = $totalVideos > 0 ? round(($completedVideos / $totalVideos) * 100, 1) : 0;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($course['title']); ?> - My Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary: #007bff;
            --primary-light: #66b3ff;
            --secondary: #6c757d;
            --success: #28a745;
            --info: #17a2b8;
            --warning: #ffc107;
            --danger: #dc3545;
            --light: #f8f9fa;
            --dark: #343a40;
            --surface: #ffffff;
            --border: #dee2e6;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            --shadow-sm: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.05);
            --radius: 0.375rem;
            --radius-sm: 0.25rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --spacing-2xl: 3rem;
        }

        body {
            background: var(--light);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        .main-card {
            background: var(--surface);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
            overflow: hidden;
            margin-bottom: var(--spacing-xl);
        }

        .header-section {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            color: white;
            padding: var(--spacing-2xl) var(--spacing-xl);
        }

        .breadcrumb-nav {
            background: var(--surface);
            border-radius: var(--radius);
            padding: var(--spacing-sm) var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .video-card {
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
            transition: all 0.2s ease;
            position: relative;
        }

        .video-card.unlocked {
            cursor: pointer;
        }

        .video-card.unlocked:hover {
            box-shadow: var(--shadow);
            transform: translateY(-2px);
        }

        .video-card.locked {
            opacity: 0.6;
            background: var(--light);
        }

        .video-card.completed {
            border-left: 4px solid var(--success);
        }

        .video-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
            color: var(--text-primary);
        }

        .video-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-sm);
        }

        .video-status {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .progress-bar {
            background: var(--border);
            border-radius: var(--radius-sm);
            height: 6px;
            overflow: hidden;
            margin-top: var(--spacing-sm);
        }

        .progress-fill {
            background: var(--success);
            height: 100%;
            transition: width 0.3s ease;
        }

        .btn-primary {
            background: var(--primary);
            border: 1px solid var(--primary);
            color: white;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background: var(--primary-light);
            border-color: var(--primary-light);
            color: white;
            text-decoration: none;
        }

        .btn-secondary {
            background: var(--secondary);
            border: 1px solid var(--secondary);
            color: white;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-weight: 500;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .stat-card {
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            text-align: center;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: var(--spacing-xs);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        @media (max-width: 768px) {
            .dashboard-container {
                padding: var(--spacing-md);
            }
            
            .header-section {
                padding: var(--spacing-xl) var(--spacing-md);
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="main-card">
            <!-- Header Section -->
            <div class="header-section">
                <h1 class="mb-2">
                    <i class="bi bi-collection-play me-2"></i>
                    <?php echo htmlspecialchars($course['title']); ?>
                </h1>
                <?php if ($course['description']): ?>
                <p class="mb-0 opacity-75"><?php echo htmlspecialchars($course['description']); ?></p>
                <?php endif; ?>
            </div>

            <!-- Content Section -->
            <div class="p-4">
                <!-- Breadcrumb -->
                <nav class="breadcrumb-nav">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item">
                            <a href="index.php?token=<?php echo urlencode($token); ?>">Dashboard</a>
                        </li>
                        <li class="breadcrumb-item active"><?php echo htmlspecialchars($course['title']); ?></li>
                    </ol>
                </nav>

                <!-- Stats Grid -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value"><?php echo $totalVideos; ?></div>
                        <div class="stat-label">Total Videos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value"><?php echo $completedVideos; ?></div>
                        <div class="stat-label">Completed</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value"><?php echo $unlockedVideos; ?></div>
                        <div class="stat-label">Unlocked</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value"><?php echo $progress; ?>%</div>
                        <div class="stat-label">Progress</div>
                    </div>
                </div>

                <!-- Videos List -->
                <h3 class="mb-4">
                    <i class="bi bi-play-circle me-2"></i>
                    Course Videos
                </h3>

                <?php if (empty($videos)): ?>
                <div class="text-center text-muted py-5">
                    <i class="bi bi-collection display-1 mb-3"></i>
                    <h4>No videos available</h4>
                    <p>Videos will appear here once they are added to the course.</p>
                </div>
                <?php else: ?>
                <?php foreach ($videos as $video): ?>
                <div class="video-card <?php echo $video['is_unlocked'] ? 'unlocked' : 'locked'; ?> <?php echo $video['is_completed'] ? 'completed' : ''; ?>" 
                     <?php if ($video['is_unlocked']): ?>onclick="watchVideo('<?php echo $video['vimeo_video_id']; ?>', <?php echo $video['id']; ?>)"<?php endif; ?>>
                    
                    <div class="video-title">
                        <?php echo htmlspecialchars($video['title']); ?>
                        <?php if ($video['is_completed']): ?>
                        <i class="bi bi-check-circle-fill text-success ms-2"></i>
                        <?php endif; ?>
                    </div>
                    
                    <?php if ($video['description']): ?>
                    <p class="text-muted mb-2"><?php echo htmlspecialchars($video['description']); ?></p>
                    <?php endif; ?>
                    
                    <div class="video-meta">
                        <div class="video-status">
                            <?php if ($video['is_unlocked']): ?>
                                <?php if ($video['is_completed']): ?>
                                <i class="bi bi-check-circle-fill text-success"></i>
                                <span class="text-success">Completed</span>
                                <?php else: ?>
                                <i class="bi bi-play-circle text-primary"></i>
                                <span class="text-primary">Available</span>
                                <?php endif; ?>
                            <?php else: ?>
                            <i class="bi bi-lock text-muted"></i>
                            <span class="text-muted">
                                Unlocks on <?php echo date('M j, Y', strtotime($video['unlock_date'])); ?>
                            </span>
                            <?php endif; ?>
                        </div>
                        <div>
                            <span class="badge bg-light text-dark">Day <?php echo $video['unlock_day']; ?></span>
                        </div>
                    </div>
                    
                    <?php if ($video['completion_percentage'] > 0): ?>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: <?php echo $video['completion_percentage']; ?>%"></div>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Video Modal -->
    <div class="modal fade" id="videoModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="videoModalTitle">Video</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body p-0">
                    <div class="ratio ratio-16x9">
                        <iframe id="videoFrame" src="" frameborder="0" allow="autoplay; fullscreen; picture-in-picture" allowfullscreen></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function watchVideo(vimeoId, videoId) {
            const videoFrame = document.getElementById('videoFrame');
            videoFrame.src = `https://player.vimeo.com/video/${vimeoId}?autoplay=1`;
            
            const videoModal = new bootstrap.Modal(document.getElementById('videoModal'));
            videoModal.show();
            
            // Track video start
            fetch('../api/videos/watch.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    video_id: videoId,
                    action: 'start',
                    token: '<?php echo $token; ?>'
                })
            });
            
            // Clear iframe when modal is closed
            document.getElementById('videoModal').addEventListener('hidden.bs.modal', function () {
                videoFrame.src = '';
            });
        }
    </script>
</body>
</html>

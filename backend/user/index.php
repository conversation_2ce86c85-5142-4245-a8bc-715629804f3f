<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

// Get user token from URL
$token = $_GET['token'] ?? '';

if (empty($token)) {
    die('Access token required');
}

$database = new Database();
$db = $database->getConnection();

// Validate token and get user
$stmt = $db->prepare("SELECT * FROM users WHERE unique_token = ? AND is_active = 1");
$stmt->execute([$token]);
$user = $stmt->fetch();

if (!$user) {
    die('Invalid or expired token');
}

// Get user's courses
$stmt = $db->prepare("
    SELECT c.*, uc.assigned_at, uc.started_at, uc.completed_at,
           COUNT(cv.id) as total_videos,
           COUNT(CASE WHEN uvp.is_completed = 1 THEN 1 END) as completed_videos
    FROM user_courses uc
    JOIN courses c ON uc.course_id = c.id
    LEFT JOIN course_videos cv ON c.id = cv.course_id AND cv.is_active = 1
    LEFT JOIN user_video_progress uvp ON cv.id = uvp.video_id AND uvp.user_id = ?
    WHERE uc.user_id = ? AND uc.is_active = 1 AND c.is_active = 1
    GROUP BY c.id, c.title, c.description, c.duration_days, c.video_unlock_interval,
             c.is_active, c.created_by, c.created_at, c.updated_at,
             uc.assigned_at, uc.started_at, uc.completed_at
    ORDER BY uc.assigned_at DESC
");
$stmt->execute([$user['id'], $user['id']]);
$courses = $stmt->fetchAll();

// Calculate overall progress
$totalVideos = 0;
$completedVideos = 0;
foreach ($courses as $course) {
    $totalVideos += $course['total_videos'];
    $completedVideos += $course['completed_videos'];
}
$overallProgress = $totalVideos > 0 ? round(($completedVideos / $totalVideos) * 100, 1) : 0;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Dashboard - <?php echo htmlspecialchars($user['full_name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary: #007bff;
            --primary-light: #66b3ff;
            --secondary: #6c757d;
            --success: #28a745;
            --info: #17a2b8;
            --warning: #ffc107;
            --danger: #dc3545;
            --light: #f8f9fa;
            --dark: #343a40;
            --surface: #ffffff;
            --border: #dee2e6;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            --shadow-sm: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.05);
            --radius: 0.375rem;
            --radius-sm: 0.25rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --spacing-2xl: 3rem;
        }

        body {
            background: var(--light);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        .main-card {
            background: var(--surface);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
            overflow: hidden;
            margin-bottom: var(--spacing-xl);
        }

        .header-section {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            color: white;
            padding: var(--spacing-2xl) var(--spacing-xl);
        }

        .dashboard-title {
            font-size: 2rem;
            font-weight: 700;
            margin: 0 0 var(--spacing-xs) 0;
        }

        .dashboard-subtitle {
            font-size: 1rem;
            margin: 0;
            opacity: 0.9;
        }

        .content-section {
            padding: var(--spacing-xl);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .stat-card {
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: var(--spacing-xs);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .course-card {
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
            transition: all 0.2s ease;
        }

        .course-card:hover {
            box-shadow: var(--shadow);
            transform: translateY(-2px);
        }

        .course-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
            color: var(--text-primary);
        }

        .course-description {
            color: var(--text-secondary);
            margin-bottom: var(--spacing-md);
        }

        .progress-bar {
            background: var(--border);
            border-radius: var(--radius-sm);
            height: 8px;
            overflow: hidden;
            margin-bottom: var(--spacing-sm);
        }

        .progress-fill {
            background: var(--success);
            height: 100%;
            transition: width 0.3s ease;
        }

        .course-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .btn-primary {
            background: var(--primary);
            border: 1px solid var(--primary);
            color: white;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background: var(--primary-light);
            border-color: var(--primary-light);
            color: white;
            text-decoration: none;
        }

        @media (max-width: 768px) {
            .dashboard-container {
                padding: var(--spacing-md);
            }
            
            .header-section {
                padding: var(--spacing-xl) var(--spacing-md);
            }
            
            .content-section {
                padding: var(--spacing-md);
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="main-card">
            <!-- Header Section -->
            <div class="header-section">
                <h1 class="dashboard-title">
                    <i class="bi bi-person-circle me-2"></i>
                    Welcome, <?php echo htmlspecialchars($user['full_name']); ?>!
                </h1>
                <p class="dashboard-subtitle">Your Weight Loss Journey Dashboard</p>
            </div>

            <!-- Content Section -->
            <div class="content-section">
                <!-- Stats Grid -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value"><?php echo count($courses); ?></div>
                        <div class="stat-label">Active Courses</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value"><?php echo $totalVideos; ?></div>
                        <div class="stat-label">Total Videos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value"><?php echo $completedVideos; ?></div>
                        <div class="stat-label">Completed Videos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value"><?php echo $overallProgress; ?>%</div>
                        <div class="stat-label">Overall Progress</div>
                    </div>
                </div>

                <!-- Courses Section -->
                <h2 class="mb-4">
                    <i class="bi bi-collection-play me-2"></i>
                    My Courses
                </h2>

                <?php if (empty($courses)): ?>
                <div class="text-center text-muted py-5">
                    <i class="bi bi-collection display-1 mb-3"></i>
                    <h4>No courses assigned yet</h4>
                    <p>Contact your administrator to get started with your weight loss journey.</p>
                </div>
                <?php else: ?>
                <?php foreach ($courses as $course): ?>
                <?php 
                $courseProgress = $course['total_videos'] > 0 ? 
                    round(($course['completed_videos'] / $course['total_videos']) * 100, 1) : 0;
                ?>
                <div class="course-card">
                    <div class="course-title"><?php echo htmlspecialchars($course['title']); ?></div>
                    <?php if ($course['description']): ?>
                    <div class="course-description"><?php echo htmlspecialchars($course['description']); ?></div>
                    <?php endif; ?>
                    
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: <?php echo $courseProgress; ?>%"></div>
                    </div>
                    
                    <div class="course-stats">
                        <span><?php echo $course['completed_videos']; ?> of <?php echo $course['total_videos']; ?> videos completed</span>
                        <span><?php echo $courseProgress; ?>% complete</span>
                    </div>
                    
                    <div class="mt-3">
                        <a href="course.php?token=<?php echo urlencode($token); ?>&course_id=<?php echo $course['id']; ?>" class="btn-primary">
                            <i class="bi bi-play-circle"></i>
                            Continue Course
                        </a>
                    </div>
                </div>
                <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

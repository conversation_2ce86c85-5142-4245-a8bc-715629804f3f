<?php
session_start();
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/Auth.php';

// Simple admin login for testing
if (!isset($_SESSION['admin_id'])) {
    $_SESSION['admin_id'] = 1; // Set admin ID for testing
}

$database = new Database();
$db = $database->getConnection();
$auth = new Auth();

// Get first user for testing
$stmt = $db->query("SELECT * FROM users WHERE is_active = 1 LIMIT 1");
$user = $stmt->fetch();

if (!$user) {
    die('No users found. Run test_system.php first.');
}

// Generate auth link
$userId = $user['id'];
$adminId = $_SESSION['admin_id'];

// Generate temporary access token (1 hour expiry)
$accessToken = generateUniqueToken(64);
$expiresAt = gmdate('Y-m-d H:i:s', time() + 3600); // 1 hour expiry in UTC

$stmt = $db->prepare("
    INSERT INTO auth_tokens (user_id, token, expires_at, created_by) 
    VALUES (?, ?, ?, ?)
");
$stmt->execute([$userId, $accessToken, $expiresAt, $adminId]);

// Generate the auth links
$baseUrl = rtrim(APP_URL, '/');
$webAuthLink = $baseUrl . '/?token=' . $accessToken;
$phpAuthLink = $baseUrl . '/user/index.php?token=' . $user['unique_token'];

echo "<h1>Auth Link Test</h1>";
echo "<h2>User: " . htmlspecialchars($user['full_name']) . "</h2>";
echo "<p>Email: " . htmlspecialchars($user['email']) . "</p>";

echo "<h3>Temporary Access Link (Auto-Login)</h3>";
echo "<p>Expires: " . $expiresAt . "</p>";
echo "<p><strong>Web Auth Link:</strong></p>";
echo "<div style='background: #f5f5f5; padding: 10px; border-radius: 5px; margin: 10px 0; word-break: break-all;'>";
echo "<a href='" . $webAuthLink . "' target='_blank'>" . $webAuthLink . "</a>";
echo "</div>";

echo "<h3>Permanent Access Link</h3>";
echo "<p><strong>PHP Auth Link:</strong></p>";
echo "<div style='background: #f5f5f5; padding: 10px; border-radius: 5px; margin: 10px 0; word-break: break-all;'>";
echo "<a href='" . $phpAuthLink . "' target='_blank'>" . $phpAuthLink . "</a>";
echo "</div>";

echo "<h3>Tokens</h3>";
echo "<p><strong>Access Token (Temporary):</strong></p>";
echo "<div style='background: #f5f5f5; padding: 10px; border-radius: 5px; margin: 10px 0; word-break: break-all; font-family: monospace;'>";
echo $accessToken;
echo "</div>";

echo "<p><strong>Permanent Token:</strong></p>";
echo "<div style='background: #f5f5f5; padding: 10px; border-radius: 5px; margin: 10px 0; word-break: break-all; font-family: monospace;'>";
echo $user['unique_token'];
echo "</div>";

echo "<h3>Test Links</h3>";
echo "<p><a href='" . $webAuthLink . "' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Auto-Login Link</a></p>";
echo "<p><a href='" . $phpAuthLink . "' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Permanent Link</a></p>";

echo "<h3>Debug Info</h3>";
echo "<p>APP_URL: " . APP_URL . "</p>";
echo "<p>Base URL: " . $baseUrl . "</p>";
echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>Token Expires: " . $expiresAt . "</p>";

// Log activity
logActivity("Test auth link generated for user {$userId}", 'INFO');
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
a { color: #007bff; }
</style>

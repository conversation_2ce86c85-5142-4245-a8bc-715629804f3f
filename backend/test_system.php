<?php
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/includes/functions.php';

$database = new Database();
$db = $database->getConnection();

echo "<h1>System Test & Sample Data Creation</h1>";

try {
    // Check if admin exists
    $stmt = $db->query("SELECT COUNT(*) as count FROM admins");
    $adminCount = $stmt->fetch()['count'];
    
    if ($adminCount == 0) {
        echo "<h2>Creating default admin...</h2>";
        $stmt = $db->prepare("INSERT INTO admins (username, email, password_hash) VALUES (?, ?, ?)");
        $stmt->execute(['admin', '<EMAIL>', password_hash('admin123', PASSWORD_DEFAULT)]);
        echo "✅ Default admin created (username: admin, password: admin123)<br>";
    } else {
        echo "✅ Admin exists<br>";
    }
    
    // Check if courses exist
    $stmt = $db->query("SELECT COUNT(*) as count FROM courses");
    $courseCount = $stmt->fetch()['count'];
    
    if ($courseCount == 0) {
        echo "<h2>Creating sample courses...</h2>";
        
        // Create sample courses
        $courses = [
            [
                'title' => 'Weight Loss Fundamentals',
                'description' => 'Learn the basics of healthy weight loss and sustainable lifestyle changes.',
                'duration_days' => 56,
                'video_unlock_interval' => 7
            ],
            [
                'title' => 'Advanced Nutrition Guide',
                'description' => 'Deep dive into nutrition science and meal planning for optimal results.',
                'duration_days' => 42,
                'video_unlock_interval' => 5
            ]
        ];
        
        foreach ($courses as $course) {
            $stmt = $db->prepare("
                INSERT INTO courses (title, description, duration_days, video_unlock_interval, created_by) 
                VALUES (?, ?, ?, ?, 1)
            ");
            $stmt->execute([$course['title'], $course['description'], $course['duration_days'], $course['video_unlock_interval']]);
            echo "✅ Created course: " . $course['title'] . "<br>";
        }
    } else {
        echo "✅ Courses exist<br>";
    }
    
    // Check if videos exist
    $stmt = $db->query("SELECT COUNT(*) as count FROM course_videos");
    $videoCount = $stmt->fetch()['count'];
    
    if ($videoCount == 0) {
        echo "<h2>Creating sample videos...</h2>";
        
        // Get first course ID
        $stmt = $db->query("SELECT id FROM courses ORDER BY id LIMIT 1");
        $courseId = $stmt->fetch()['id'];
        
        // Create sample videos (using placeholder Vimeo IDs)
        $videos = [
            [
                'title' => 'Welcome to Your Weight Loss Journey',
                'description' => 'Introduction to the program and setting realistic goals.',
                'vimeo_video_id' => '123456789',
                'unlock_day' => 1,
                'order_index' => 1
            ],
            [
                'title' => 'Understanding Calories and Metabolism',
                'description' => 'Learn how your body burns calories and processes food.',
                'vimeo_video_id' => '123456790',
                'unlock_day' => 8,
                'order_index' => 2
            ],
            [
                'title' => 'Building Healthy Eating Habits',
                'description' => 'Practical tips for creating sustainable eating patterns.',
                'vimeo_video_id' => '123456791',
                'unlock_day' => 15,
                'order_index' => 3
            ],
            [
                'title' => 'Exercise Fundamentals',
                'description' => 'Getting started with safe and effective exercise routines.',
                'vimeo_video_id' => '123456792',
                'unlock_day' => 22,
                'order_index' => 4
            ]
        ];
        
        foreach ($videos as $video) {
            $vimeoEmbedUrl = "https://player.vimeo.com/video/{$video['vimeo_video_id']}";
            $stmt = $db->prepare("
                INSERT INTO course_videos (course_id, title, description, vimeo_video_id, vimeo_embed_url, unlock_day, order_index) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $courseId, 
                $video['title'], 
                $video['description'], 
                $video['vimeo_video_id'], 
                $vimeoEmbedUrl, 
                $video['unlock_day'], 
                $video['order_index']
            ]);
            echo "✅ Created video: " . $video['title'] . "<br>";
        }
    } else {
        echo "✅ Videos exist<br>";
    }
    
    // Check if users exist
    $stmt = $db->query("SELECT COUNT(*) as count FROM users");
    $userCount = $stmt->fetch()['count'];
    
    if ($userCount == 0) {
        echo "<h2>Creating sample users...</h2>";
        
        // Create sample users
        $users = [
            [
                'email' => '<EMAIL>',
                'full_name' => 'John Doe',
                'age' => 35,
                'weight' => 85.5,
                'height' => 175,
                'target_weight' => 75.0,
                'phone' => '+1234567890'
            ],
            [
                'email' => '<EMAIL>',
                'full_name' => 'Jane Smith',
                'age' => 28,
                'weight' => 68.2,
                'height' => 165,
                'target_weight' => 60.0,
                'phone' => '+1234567891'
            ]
        ];
        
        foreach ($users as $user) {
            $uniqueToken = generateUniqueToken();
            $stmt = $db->prepare("
                INSERT INTO users (email, full_name, age, weight, height, target_weight, phone, unique_token, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
            ");
            $stmt->execute([
                $user['email'], 
                $user['full_name'], 
                $user['age'], 
                $user['weight'], 
                $user['height'], 
                $user['target_weight'], 
                $user['phone'], 
                $uniqueToken
            ]);
            
            $userId = $db->lastInsertId();
            
            // Assign first course to user
            $stmt = $db->prepare("INSERT INTO user_courses (user_id, course_id) VALUES (?, ?)");
            $stmt->execute([$userId, 1]);
            
            echo "✅ Created user: " . $user['full_name'] . " (Token: $uniqueToken)<br>";
            echo "🔗 User dashboard: <a href='user/index.php?token=$uniqueToken' target='_blank'>http://localhost:8000/user/index.php?token=$uniqueToken</a><br>";
        }
    } else {
        echo "✅ Users exist<br>";
        
        // Show existing user tokens
        echo "<h3>Existing User Access Links:</h3>";
        $stmt = $db->query("SELECT full_name, unique_token FROM users WHERE is_active = 1");
        $users = $stmt->fetchAll();
        
        foreach ($users as $user) {
            echo "👤 " . $user['full_name'] . ": <a href='user/index.php?token=" . $user['unique_token'] . "' target='_blank'>http://localhost:8000/user/index.php?token=" . $user['unique_token'] . "</a><br>";
        }
    }
    
    echo "<h2>System Status</h2>";
    echo "✅ Database connection: OK<br>";
    echo "✅ Admin panel: <a href='admin/login.php' target='_blank'>http://localhost:8000/admin/login.php</a> (admin/admin123)<br>";
    echo "✅ API endpoints: Available<br>";
    echo "✅ User interface: Available<br>";
    
    echo "<h2>Quick Links</h2>";
    echo "🔧 <a href='admin/courses.php' target='_blank'>Course Management</a><br>";
    echo "👥 <a href='admin/users.php' target='_blank'>User Management</a><br>";
    echo "📊 <a href='admin/dashboard_weightloss.php' target='_blank'>Admin Dashboard</a><br>";
    
    echo "<h2>API Test</h2>";
    echo "📡 <a href='api/admin/courses.php' target='_blank'>Courses API</a><br>";
    echo "📡 <a href='api/admin/videos.php' target='_blank'>Videos API</a><br>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>

<?php
/**
 * Additional Functions (avoiding redeclaration of functions in config.php)
 */

/**
 * Validate required fields in input
 */
if (!function_exists('validateRequired')) {
    function validateRequired($input, $required) {
        foreach ($required as $field) {
            if (!isset($input[$field]) || empty($input[$field])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => "Missing required field: $field"
                ]);
                exit;
            }
        }
    }
}

/**
 * Send JSON response
 */
if (!function_exists('sendJsonResponse')) {
    function sendJsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}

/**
 * Handle CORS
 */
if (!function_exists('handleCORS')) {
    function handleCORS() {
        header('Access-Control-Allow-Origin: http://localhost:3000');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, X-Device-Fingerprint');

        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit;
        }
    }
}

if (!function_exists('sanitizeInput')) {
    function sanitizeInput($input) {
        return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
    }
}

/**
 * Format duration in seconds to human readable format
 */
if (!function_exists('formatDuration')) {
    function formatDuration($seconds) {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $seconds = $seconds % 60;

        if ($hours > 0) {
            return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
        } else {
            return sprintf('%02d:%02d', $minutes, $seconds);
        }
    }
}

/**
 * Calculate BMI
 */
if (!function_exists('calculateBMI')) {
    function calculateBMI($weight, $height) {
        if ($weight <= 0 || $height <= 0) {
            return null;
        }
        $heightInMeters = $height / 100;
        return round($weight / ($heightInMeters * $heightInMeters), 1);
    }
}

/**
 * Get BMI category
 */
if (!function_exists('getBMICategory')) {
    function getBMICategory($bmi) {
        if ($bmi < 18.5) return 'Underweight';
        if ($bmi < 25) return 'Normal';
        if ($bmi < 30) return 'Overweight';
        return 'Obese';
    }
}

/**
 * Generate course progress percentage
 */
if (!function_exists('calculateCourseProgress')) {
    function calculateCourseProgress($db, $userId, $courseId) {
        try {
            // Get total videos in course
            $stmt = $db->prepare("SELECT COUNT(*) as total FROM course_videos WHERE course_id = ? AND is_active = 1");
            $stmt->execute([$courseId]);
            $total = $stmt->fetch()['total'];

            if ($total == 0) return 0;

            // Get completed videos
            $stmt = $db->prepare("
                SELECT COUNT(*) as completed
                FROM user_video_progress uvp
                JOIN course_videos cv ON uvp.video_id = cv.id
                WHERE uvp.user_id = ? AND cv.course_id = ? AND uvp.is_completed = 1
            ");
            $stmt->execute([$userId, $courseId]);
            $completed = $stmt->fetch()['completed'];

            return round(($completed / $total) * 100, 1);
        } catch (Exception $e) {
            return 0;
        }
    }
}

/**
 * Check if video is unlocked for user
 */
if (!function_exists('isVideoUnlocked')) {
    function isVideoUnlocked($db, $userId, $videoId) {
        try {
            $stmt = $db->prepare("
                SELECT cv.unlock_day, uc.started_at
                FROM course_videos cv
                JOIN user_courses uc ON cv.course_id = uc.course_id
                WHERE cv.id = ? AND uc.user_id = ? AND uc.is_active = 1
            ");
            $stmt->execute([$videoId, $userId]);
            $result = $stmt->fetch();

            if (!$result || !$result['started_at']) {
                return false;
            }

            $startDate = new DateTime($result['started_at']);
            $unlockDate = $startDate->add(new DateInterval('P' . ($result['unlock_day'] - 1) . 'D'));
            $now = new DateTime();

            return $now >= $unlockDate;
        } catch (Exception $e) {
            return false;
        }
    }
}

/**
 * Get user's next unlockable video
 */
if (!function_exists('getNextUnlockableVideo')) {
    function getNextUnlockableVideo($db, $userId, $courseId) {
        try {
            $stmt = $db->prepare("
                SELECT cv.*, uc.started_at,
                       DATE_ADD(uc.started_at, INTERVAL (cv.unlock_day - 1) DAY) as unlock_date
                FROM course_videos cv
                JOIN user_courses uc ON cv.course_id = uc.course_id
                LEFT JOIN user_video_progress uvp ON cv.id = uvp.video_id AND uvp.user_id = uc.user_id
                WHERE uc.user_id = ? AND cv.course_id = ? AND cv.is_active = 1
                      AND uc.is_active = 1 AND (uvp.is_completed IS NULL OR uvp.is_completed = 0)
                      AND DATE_ADD(uc.started_at, INTERVAL (cv.unlock_day - 1) DAY) > NOW()
                ORDER BY cv.unlock_day ASC, cv.order_index ASC
                LIMIT 1
            ");
            $stmt->execute([$userId, $courseId]);
            return $stmt->fetch();
        } catch (Exception $e) {
            return null;
        }
    }
}
?>

<?php
require_once __DIR__ . '/JWT.php';
require_once __DIR__ . '/../config/database.php';

/**
 * Authentication Handler
 */
class Auth {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    /**
     * Authenticate user with unique token
     */
    public function authenticateWithToken($token) {
        try {
            $stmt = $this->db->prepare("
                SELECT u.*, uc.course_id 
                FROM users u 
                LEFT JOIN user_courses uc ON u.id = uc.user_id AND uc.is_active = 1
                WHERE u.unique_token = ? AND u.is_active = 1
            ");
            $stmt->execute([$token]);
            $user = $stmt->fetch();
            
            if (!$user) {
                return false;
            }
            
            // Update last login
            $this->updateLastLogin($user['id']);
            
            // Generate JWT token
            $jwtToken = JWT::generateToken($user['id'], 'user');
            
            return [
                'user' => $user,
                'jwt_token' => $jwtToken
            ];
            
        } catch (Exception $e) {
            logActivity("Authentication error: " . $e->getMessage(), 'ERROR');
            return false;
        }
    }
    
    /**
     * Authenticate admin user
     */
    public function authenticateAdmin($username, $password) {
        try {
            $stmt = $this->db->prepare("SELECT * FROM admins WHERE username = ? OR email = ?");
            $stmt->execute([$username, $username]);
            $admin = $stmt->fetch();
            
            if (!$admin || !verifyPassword($password, $admin['password_hash'])) {
                return false;
            }
            
            $jwtToken = JWT::generateToken($admin['id'], 'admin');
            
            return [
                'admin' => $admin,
                'jwt_token' => $jwtToken
            ];
            
        } catch (Exception $e) {
            logActivity("Admin authentication error: " . $e->getMessage(), 'ERROR');
            return false;
        }
    }
    
    /**
     * Validate JWT token and get user info
     */
    public function validateJWT($token) {
        $payload = JWT::validateToken($token);
        
        if (!$payload) {
            return false;
        }
        
        $table = $payload['user_type'] === 'admin' ? 'admins' : 'users';
        $stmt = $this->db->prepare("SELECT * FROM {$table} WHERE id = ?");
        $stmt->execute([$payload['user_id']]);
        
        return $stmt->fetch();
    }
    
    /**
     * Generate unique authentication link for user
     */
    public function generateAuthLink($userId, $adminId) {
        try {
            $token = generateUniqueToken(64);
            $expiresAt = gmdate('Y-m-d H:i:s', time() + 3600); // 1 hour expiry in UTC
            
            $stmt = $this->db->prepare("
                INSERT INTO auth_tokens (user_id, token, expires_at, created_by) 
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$userId, $token, $expiresAt, $adminId]);
            
            return APP_URL . "/?token=" . $token;
            
        } catch (Exception $e) {
            logActivity("Auth link generation error: " . $e->getMessage(), 'ERROR');
            return false;
        }
    }
    
    /**
     * Use authentication token (mark as used)
     */
    public function useAuthToken($token) {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM auth_tokens
                WHERE token = ? AND expires_at > UTC_TIMESTAMP() AND is_used = 0
            ");
            $stmt->execute([$token]);
            $authToken = $stmt->fetch();
            
            if (!$authToken) {
                return false;
            }
            
            // Mark token as used
            $stmt = $this->db->prepare("
                UPDATE auth_tokens
                SET is_used = 1, used_at = UTC_TIMESTAMP()
                WHERE id = ?
            ");
            $stmt->execute([$authToken['id']]);
            
            return $authToken['user_id'];
            
        } catch (Exception $e) {
            logActivity("Auth token usage error: " . $e->getMessage(), 'ERROR');
            return false;
        }
    }
    
    /**
     * Create user session
     */
    public function createSession($userId, $deviceInfo = null) {
        try {
            $sessionToken = generateUniqueToken(32);
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            
            $stmt = $this->db->prepare("
                INSERT INTO user_sessions (user_id, session_token, device_info, ip_address)
                VALUES (?, ?, ?, ?)
            ");
            $deviceInfoJson = is_array($deviceInfo) ? json_encode($deviceInfo) : $deviceInfo;
            $stmt->execute([$userId, $sessionToken, $deviceInfoJson, $ipAddress]);
            
            return $sessionToken;
            
        } catch (Exception $e) {
            logActivity("Session creation error: " . $e->getMessage(), 'ERROR');
            return false;
        }
    }
    
    /**
     * Update last login timestamp
     */
    private function updateLastLogin($userId) {
        $stmt = $this->db->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
        $stmt->execute([$userId]);
    }
    
    /**
     * Revoke user access
     */
    public function revokeUserAccess($userId) {
        try {
            $this->db->beginTransaction();
            
            // Deactivate user
            $stmt = $this->db->prepare("UPDATE users SET is_active = 0 WHERE id = ?");
            $stmt->execute([$userId]);
            
            // End all active sessions
            $stmt = $this->db->prepare("
                UPDATE user_sessions 
                SET ended_at = NOW() 
                WHERE user_id = ? AND ended_at IS NULL
            ");
            $stmt->execute([$userId]);
            
            // Mark all unused auth tokens as used
            $stmt = $this->db->prepare("
                UPDATE auth_tokens 
                SET is_used = 1, used_at = NOW() 
                WHERE user_id = ? AND is_used = 0
            ");
            $stmt->execute([$userId]);
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            logActivity("User access revocation error: " . $e->getMessage(), 'ERROR');
            return false;
        }
    }
}
?>

@echo off
REM Weight Loss Dashboard - Frontend Server Startup Script (Windows)

echo 🚀 Starting Weight Loss Dashboard Frontend (Flutter PWA)...
echo ==========================================================

REM Check if Flutter is installed
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Flutter is not installed. Please install Flutter SDK.
    echo    Visit: https://docs.flutter.dev/get-started/install
    echo    Download from: https://flutter.dev/docs/get-started/install
    pause
    exit /b 1
)

REM Show Flutter version
echo ✅ Flutter is installed
flutter --version | findstr Flutter

REM Navigate to frontend directory
cd frontend

REM Check if pubspec.yaml exists
if not exist "pubspec.yaml" (
    echo ❌ pubspec.yaml not found. Make sure you're in the correct directory.
    pause
    exit /b 1
)

REM Install dependencies
echo 📦 Installing Flutter dependencies...
flutter pub get

REM Enable web support
echo 🌐 Enabling Flutter web support...
flutter config --enable-web

REM Update constants for local development
echo ⚙️  Configuring for local development...

REM Create local config override
echo // Local development configuration > lib\utils\local_config.dart
echo class LocalConfig { >> lib\utils\local_config.dart
echo   static const String baseUrl = 'http://localhost:8000/api'; >> lib\utils\local_config.dart
echo   static const String adminUrl = 'http://localhost:8000/admin'; >> lib\utils\local_config.dart
echo } >> lib\utils\local_config.dart

REM Update constants file for local development
if exist "lib\utils\constants.dart" (
    powershell -Command "(gc lib\utils\constants.dart) -replace 'http://localhost/api', 'http://localhost:8000/api' | Out-File -encoding ASCII lib\utils\constants.dart"
    powershell -Command "(gc lib\utils\constants.dart) -replace 'http://localhost/admin', 'http://localhost:8000/admin' | Out-File -encoding ASCII lib\utils\constants.dart"
)

echo 🌐 Starting Flutter web development server...
echo    Frontend URL: http://localhost:3000
echo    Backend API: http://localhost:8000/api
echo.
echo Make sure the backend server is running on http://localhost:8000
echo Press Ctrl+C to stop the server
echo ==========================================================

REM Start Flutter web server
flutter run -d web-server --web-port 3000 --web-hostname localhost

#!/bin/bash

# Weight Loss Dashboard - Stop All Servers

echo "🛑 Stopping Weight Loss Dashboard Servers..."
echo "============================================="

# Stop PHP server
echo "🔧 Stopping Backend Server (PHP)..."
if pgrep -f "php.*localhost:8000" > /dev/null; then
    pkill -f "php.*localhost:8000"
    echo "   ✅ PHP server stopped"
else
    echo "   ℹ️  PHP server was not running"
fi

# Stop Flutter server
echo "📱 Stopping Frontend Server (Flutter)..."
if pgrep -f "flutter.*web-server" > /dev/null; then
    pkill -f "flutter.*web-server"
    echo "   ✅ Flutter server stopped"
else
    echo "   ℹ️  Flutter server was not running"
fi

# Wait a moment for processes to terminate
sleep 2

# Verify servers are stopped
echo ""
echo "🔍 Verifying servers are stopped..."

if ! curl -s http://localhost:8000 > /dev/null 2>&1; then
    echo "   ✅ Backend server (port 8000) is stopped"
else
    echo "   ⚠️  Backend server may still be running"
fi

if ! curl -s http://localhost:3000 > /dev/null 2>&1; then
    echo "   ✅ Frontend server (port 3000) is stopped"
else
    echo "   ⚠️  Frontend server may still be running"
fi

echo ""
echo "============================================="
echo "🎉 Server shutdown complete!"
echo ""
echo "💡 To start servers again:"
echo "   Backend:  ./start-backend.sh"
echo "   Frontend: ./start-frontend.sh"
echo "   Status:   ./check-status.sh"

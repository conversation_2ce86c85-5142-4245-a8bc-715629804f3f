#!/bin/bash

# Weight Loss Dashboard - Frontend Server Startup Script

echo "🚀 Starting Weight Loss Dashboard Frontend (Flutter PWA)..."
echo "=========================================================="

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed. Please install Flutter SDK."
    echo "   Visit: https://docs.flutter.dev/get-started/install"
    echo "   macOS: brew install --cask flutter"
    echo "   Or download from: https://flutter.dev/docs/get-started/install"
    exit 1
fi

# Check Flutter version
echo "✅ Flutter is installed"
flutter --version | head -1

# Navigate to frontend directory
cd frontend

# Check if pubspec.yaml exists
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ pubspec.yaml not found. Make sure you're in the correct directory."
    exit 1
fi

# Install dependencies
echo "📦 Installing Flutter dependencies..."
flutter pub get

# Check if web is enabled
echo "🌐 Enabling Flutter web support..."
flutter config --enable-web

# Update constants for local development
echo "⚙️  Configuring for local development..."

# Create a local config override
cat > lib/utils/local_config.dart << 'EOF'
// Local development configuration
class LocalConfig {
  static const String baseUrl = 'http://localhost:8000/api';
  static const String adminUrl = 'http://localhost:8000/admin';
}
EOF

# Update the constants file to use local config in development
if [ -f "lib/utils/constants.dart" ]; then
    # Backup original
    cp lib/utils/constants.dart lib/utils/constants.dart.backup
    
    # Update for local development
    sed -i.bak "s|http://localhost/api|http://localhost:8000/api|g" lib/utils/constants.dart
    sed -i.bak "s|http://localhost/admin|http://localhost:8000/admin|g" lib/utils/constants.dart
fi

echo "🌐 Starting Flutter web development server..."
echo "   Frontend URL: http://localhost:3000"
echo "   Backend API: http://localhost:8000/api"
echo ""
echo "Make sure the backend server is running on http://localhost:8000"
echo "Press Ctrl+C to stop the server"
echo "=========================================================="

# Start Flutter web server
flutter run -d web-server --web-port 3000 --web-hostname localhost

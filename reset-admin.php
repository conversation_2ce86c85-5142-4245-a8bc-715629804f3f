<?php
/**
 * Reset Admin Password Script
 */

if ($argc < 4) {
    echo "Usage: php reset-admin.php <username> <email> <password>\n";
    echo "Example: php reset-admin.<NAME_EMAIL> newpassword123\n";
    exit(1);
}

$username = $argv[1];
$email = $argv[2];
$password = $argv[3];

try {
    require_once 'backend/config/config.php';
    require_once 'backend/config/database.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    // Hash the password
    $passwordHash = hashPassword($password);
    
    // Update admin user
    $stmt = $db->prepare("
        UPDATE admins 
        SET username = ?, email = ?, password_hash = ? 
        WHERE id = 1
    ");
    $stmt->execute([$username, $email, $passwordHash]);
    
    echo "✅ Admin account updated successfully!\n";
    echo "   Username: $username\n";
    echo "   Email: $email\n";
    echo "   Password: $password\n";
    echo "\n";
    echo "You can now login at: http://localhost:8000/admin/\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>

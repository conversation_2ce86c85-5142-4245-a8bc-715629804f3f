@echo off
REM Weight Loss Dashboard - Backend Server Startup Script (Windows)

echo 🚀 Starting Weight Loss Dashboard Backend Server...
echo ==================================================

REM Check if PHP is installed
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PHP is not installed. Please install PHP 8.0 or higher.
    echo    Download from https://www.php.net/downloads
    echo    Or use XAMPP: https://www.apachefriends.org/
    pause
    exit /b 1
)

REM Show PHP version
echo ✅ PHP is installed
php -r "echo 'PHP Version: ' . PHP_VERSION . PHP_EOL;"

REM Create necessary directories
echo 📁 Creating necessary directories...
if not exist "backend\logs" mkdir backend\logs
if not exist "backend\cache" mkdir backend\cache
if not exist "backend\uploads" mkdir backend\uploads

REM Check if .env exists
if not exist "backend\.env" (
    echo 📝 Creating .env file from template...
    copy "backend\.env.example" "backend\.env"
    echo ⚠️  Please edit backend\.env with your database credentials
)

REM Start PHP development server
echo 🌐 Starting PHP development server...
echo    Backend URL: http://localhost:8000
echo    Admin Panel: http://localhost:8000/admin/
echo    API Base: http://localhost:8000/api/
echo    Setup Wizard: http://localhost:8000/setup.php
echo.
echo Press Ctrl+C to stop the server
echo ==================================================

REM Start the server
php -S localhost:8000 -t .

<?php
/**
 * Setup Status Check Script
 */

echo "🔍 Weight Loss Dashboard Setup Status\n";
echo "=====================================\n";

// Check .env file
if (file_exists('backend/.env')) {
    echo "✅ Configuration file (.env) exists\n";
} else {
    echo "❌ Configuration file (.env) missing\n";
}

// Check database connection
try {
    require_once 'backend/config/config.php';
    require_once 'backend/config/database.php';
    
    $database = new Database();
    $db = $database->getConnection();
    echo "✅ Database connection successful\n";
    
    // Check tables
    $stmt = $db->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (count($tables) > 0) {
        echo "✅ Database tables exist (" . count($tables) . " tables)\n";
        echo "   Tables: " . implode(', ', $tables) . "\n";
    } else {
        echo "❌ No database tables found\n";
    }
    
    // Check admin user
    $stmt = $db->query("SELECT COUNT(*) as count FROM admins");
    $adminCount = $stmt->fetch()['count'];
    
    if ($adminCount > 0) {
        echo "✅ Admin user exists\n";
        
        $stmt = $db->query("SELECT username, email FROM admins WHERE id = 1");
        $admin = $stmt->fetch();
        echo "   Username: " . $admin['username'] . "\n";
        echo "   Email: " . $admin['email'] . "\n";
    } else {
        echo "❌ No admin user found\n";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

// Check setup completion flag
if (file_exists('backend/setup_complete.flag')) {
    $flagContent = file_get_contents('backend/setup_complete.flag');
    echo "✅ Setup completed at: " . trim($flagContent) . "\n";
} else {
    echo "⚠️  Setup not completed (no flag file)\n";
}

echo "\n=====================================\n";

// Provide next steps
if (file_exists('backend/setup_complete.flag')) {
    echo "🎉 Setup is complete! You can:\n";
    echo "   • Access admin panel: http://localhost:8000/admin/\n";
    echo "   • View user app: http://localhost:3000\n";
    echo "   • Reset admin password: php reset-admin.php <username> <email> <password>\n";
} else {
    echo "📋 Next steps:\n";
    echo "   • Complete setup: http://localhost:8000/setup.php\n";
    echo "   • Or reset admin: php reset-admin.<NAME_EMAIL> password123\n";
}

echo "\n";
?>

#!/bin/bash

# Weight Loss Dashboard - Backend Server Startup Script

echo "🚀 Starting Weight Loss Dashboard Backend Server..."
echo "=================================================="

# Check if PHP is installed
if ! command -v php &> /dev/null; then
    echo "❌ PHP is not installed. Please install PHP 8.0 or higher."
    echo "   macOS: brew install php"
    echo "   Ubuntu: sudo apt install php8.1"
    echo "   Windows: Download from https://www.php.net/downloads"
    exit 1
fi

# Check PHP version
PHP_VERSION=$(php -r "echo PHP_VERSION;")
echo "✅ PHP Version: $PHP_VERSION"

# Check if MySQL is running (optional check)
if command -v mysql &> /dev/null; then
    echo "✅ MySQL is available"
else
    echo "⚠️  MySQL not found. You'll need a MySQL server running for full functionality."
    echo "   You can use XAMPP, MAMP, or install MySQL separately."
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p backend/logs
mkdir -p backend/cache
mkdir -p backend/uploads

# Set permissions (Unix/Linux/macOS)
if [[ "$OSTYPE" != "msys" && "$OSTYPE" != "win32" ]]; then
    chmod 755 backend/logs
    chmod 755 backend/cache
    chmod 755 backend/uploads
fi

# Check if .env exists
if [ ! -f "backend/.env" ]; then
    echo "📝 Creating .env file from template..."
    cp backend/.env.example backend/.env
    echo "⚠️  Please edit backend/.env with your database credentials"
fi

# Start PHP development server
echo "🌐 Starting PHP development server..."
echo "   Backend URL: http://localhost:8000"
echo "   Admin Panel: http://localhost:8000/admin/"
echo "   API Base: http://localhost:8000/api/"
echo "   Setup Wizard: http://localhost:8000/setup.php"
echo ""
echo "Press Ctrl+C to stop the server"
echo "=================================================="

# Start the server in the current directory so all paths work correctly
php -S localhost:8000 -t .

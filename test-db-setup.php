<?php
/**
 * Test Database Setup Script
 */

echo "Testing Database Setup...\n";
echo "========================\n";

try {
    // Load configuration
    require_once 'backend/config/config.php';
    require_once 'backend/config/database.php';
    
    echo "✅ Configuration loaded\n";
    
    // Test database connection
    $database = new Database();
    $db = $database->getConnection();
    
    echo "✅ Database connection successful\n";
    
    // Read schema file
    $schemaFile = 'backend/database/schema.sql';
    if (!file_exists($schemaFile)) {
        throw new Exception("Schema file not found: $schemaFile");
    }
    
    $schema = file_get_contents($schemaFile);
    echo "✅ Schema file loaded (" . strlen($schema) . " bytes)\n";
    
    // Split into statements
    $statements = explode(';', $schema);
    $validStatements = [];
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement) && !preg_match('/^--/', $statement)) {
            $validStatements[] = $statement;
        }
    }
    
    echo "✅ Found " . count($validStatements) . " SQL statements\n";
    
    // Execute statements
    $executed = 0;
    foreach ($validStatements as $i => $statement) {
        try {
            echo "Executing statement " . ($i + 1) . "...\n";
            $db->exec($statement);
            $executed++;
        } catch (Exception $e) {
            echo "❌ Error in statement " . ($i + 1) . ": " . $e->getMessage() . "\n";
            echo "Statement: " . substr($statement, 0, 100) . "...\n";
        }
    }
    
    echo "✅ Executed $executed statements successfully\n";
    
    // Check if tables were created
    $stmt = $db->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "✅ Created tables: " . implode(', ', $tables) . "\n";
    
    echo "\n========================\n";
    echo "Database setup completed successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>

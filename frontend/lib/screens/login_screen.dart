import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../services/auth_service.dart';
import '../utils/app_colors.dart';
import '../utils/constants.dart';
import '../widgets/custom_button.dart';
import '../widgets/custom_text_field.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with SingleTickerProviderStateMixin {
  final _tokenController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: Constants.mediumAnimation,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _tokenController.dispose();
    _animationController.dispose();
    super.dispose();
  }



  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;

    final token = _tokenController.text.trim();
    print('🔐 LOGIN_SCREEN: Starting manual login with token: ${token.substring(0, 20)}...');

    setState(() {
      _isLoading = true;
    });

    final authService = Provider.of<AuthService>(context, listen: false);
    print('📡 LOGIN_SCREEN: Calling authService.login()...');

    final success = await authService.login(token);

    print('📨 LOGIN_SCREEN: Login result: ${success ? "SUCCESS" : "FAILED"}');
    if (!success) {
      print('❌ LOGIN_SCREEN: Login error: ${authService.error}');
    }

    setState(() {
      _isLoading = false;
    });

    if (success) {
      print('✅ LOGIN_SCREEN: Login successful, navigating to home...');
      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/home');
      }
    } else {
      print('❌ LOGIN_SCREEN: Login failed, showing error dialog...');
      if (mounted) {
        _showErrorDialog(authService.error ?? 'Login failed');
      }
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Login Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _testApiConnection() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Test with a fresh valid token from the test page
      final testToken = '5ce2ad0be448f9a5ba91ed1fce890bc0ff048fc3c6fe04cca014133f7b770eba93d41fa9d6325b199a780dd09da2ad4d97572a60463df9b0bac597269a36f255';
      print('🧪 Testing direct HTTP API connection...');
      print('🧪 Token: ${testToken.substring(0, 20)}...');
      print('🧪 URL: ${Constants.baseUrl}/auth/login');

      // Test direct HTTP call
      final response = await http.post(
        Uri.parse('${Constants.baseUrl}/auth/login'),
        headers: {
          'Content-Type': 'application/json',
          'Origin': 'http://localhost:3000',
          'Access-Control-Request-Method': 'POST',
          'Access-Control-Request-Headers': 'Content-Type',
        },
        body: json.encode({
          'token': testToken,
          'device_info': {'platform': 'web', 'test': true},
        }),
      );

      print('🧪 HTTP Response Status: ${response.statusCode}');
      print('🧪 HTTP Response Headers: ${response.headers}');
      print('🧪 HTTP Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          print('🧪 ✅ Direct HTTP test SUCCESS!');
          // Now test through AuthService
          final authService = Provider.of<AuthService>(context, listen: false);
          final success = await authService.login(testToken);
          print('🧪 AuthService test result: ${success ? "SUCCESS" : "FAILED"}');

          if (success) {
            if (mounted) {
              Navigator.of(context).pushReplacementNamed('/home');
            }
          } else {
            print('🧪 AuthService error: ${authService.error}');
          }
        } else {
          print('🧪 ❌ API returned success=false: ${data['message']}');
        }
      } else {
        print('🧪 ❌ HTTP error: ${response.statusCode} ${response.reasonPhrase}');
      }
    } catch (e) {
      print('🧪 ❌ Test exception: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _launchAdminPanel() async {
    final url = Uri.parse(Constants.adminUrl);
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(Constants.largePadding),
              child: AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  return FadeTransition(
                    opacity: _fadeAnimation,
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: Card(
                        elevation: Constants.cardElevation,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(Constants.largePadding),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Header
                              _buildHeader(),
                              
                              const SizedBox(height: 32),
                              
                              // Login Form
                              _buildLoginForm(),
                              
                              const SizedBox(height: 24),
                              
                              // Admin Link
                              _buildAdminLink(),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // App Icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            gradient: AppColors.primaryGradient,
            borderRadius: BorderRadius.circular(20),
            boxShadow: AppColors.cardShadow,
          ),
          child: const Icon(
            Icons.fitness_center,
            size: 40,
            color: Colors.white,
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Welcome Text
        Text(
          'Welcome Back',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.darkGray,
          ),
        ),
        
        const SizedBox(height: 8),
        
        Text(
          'Enter your unique access token to continue',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppColors.mediumGray,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildLoginForm() {
    return Consumer<AuthService>(
      builder: (context, authService, child) {
        return Form(
          key: _formKey,
          child: Column(
            children: [
              // Debug display for AuthService messages
              if (authService.error != null)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: authService.error!.startsWith('DEBUG:')
                        ? Colors.blue.shade50
                        : Colors.red.shade50,
                    border: Border.all(
                      color: authService.error!.startsWith('DEBUG:')
                          ? Colors.blue.shade200
                          : Colors.red.shade200
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        authService.error!.startsWith('DEBUG:') ? 'Debug Info:' : 'Error:',
                        style: TextStyle(
                          color: authService.error!.startsWith('DEBUG:')
                              ? Colors.blue.shade700
                              : Colors.red.shade700,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        authService.error!,
                        style: TextStyle(
                          color: authService.error!.startsWith('DEBUG:')
                              ? Colors.blue.shade700
                              : Colors.red.shade700,
                          fontSize: 12,
                          fontFamily: 'monospace',
                        ),
                      ),
                    ],
                  ),
                ),

              // Additional debug info
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Authentication Status:',
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Authenticated: ${authService.isAuthenticated}',
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 11,
                        fontFamily: 'monospace',
                      ),
                    ),
                    Text(
                      'Loading: ${authService.isLoading}',
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 11,
                        fontFamily: 'monospace',
                      ),
                    ),
                    Text(
                      'User: ${authService.user?.fullName ?? 'None'}',
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 11,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ],
                ),
              ),

              CustomTextField(
                controller: _tokenController,
                label: 'Access Token',
                hint: 'Enter your unique access token',
                prefixIcon: Icons.vpn_key,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter your access token';
                  }
                  if (value.trim().length < 10) {
                    return 'Invalid token format';
                  }
                  return null;
                },
                enabled: !_isLoading,
              ),

              const SizedBox(height: 24),

              CustomButton(
                text: 'Login',
                onPressed: _handleLogin,
                isLoading: _isLoading,
                width: double.infinity,
              ),

              const SizedBox(height: 12),

              // Test API Connection Button
              OutlinedButton(
                onPressed: _isLoading ? null : _testApiConnection,
                child: Text('Test API Connection'),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAdminLink() {
    return Column(
      children: [
        const Divider(),
        
        const SizedBox(height: 16),
        
        Text(
          'Administrator?',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.mediumGray,
          ),
        ),
        
        const SizedBox(height: 8),
        
        TextButton(
          onPressed: _launchAdminPanel,
          child: Text(
            'Access Admin Panel',
            style: TextStyle(
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}

import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import '../utils/constants.dart';
import '../models/user.dart';
import '../models/course.dart';
import 'storage_service.dart';

class ApiService {
  late final Dio _dio;
  
  ApiService() {
    _dio = Dio(BaseOptions(
      baseUrl: Constants.baseUrl,
      connectTimeout: Constants.connectionTimeout,
      receiveTimeout: Constants.receiveTimeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        print('🌐 DIO REQUEST: ${options.method} ${options.uri}');
        print('🌐 DIO HEADERS: ${options.headers}');
        print('🌐 DIO DATA: ${options.data}');

        // Add auth token if available
        final token = StorageService.getAuthToken();
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
          print('🔐 DIO: Added auth token to request');
        }

        // Add device fingerprint
        final fingerprint = await _getDeviceFingerprint();
        options.headers['X-Device-Fingerprint'] = fingerprint;
        print('📱 DIO: Added device fingerprint: $fingerprint');

        handler.next(options);
      },
      onResponse: (response, handler) {
        print('📨 DIO RESPONSE: ${response.statusCode} ${response.statusMessage}');
        print('📄 DIO RESPONSE DATA: ${response.data}');
        handler.next(response);
      },
      onError: (error, handler) {
        print('💥 DIO ERROR: ${error.message}');
        print('💥 DIO ERROR TYPE: ${error.type}');
        print('💥 DIO ERROR RESPONSE: ${error.response?.data}');

        // Handle token expiration
        if (error.response?.statusCode == 401) {
          _handleUnauthorized();
        }
        handler.next(error);
      },
    ));
  }

  // Authentication
  Future<Map<String, dynamic>> login(String token) async {
    print('🌐 ApiService.login: Starting API login request...');
    print('🔗 ApiService.login: Base URL: ${_dio.options.baseUrl}');
    print('🎫 ApiService.login: Token: ${token.substring(0, 20)}...');

    try {
      print('📱 ApiService.login: Getting device info...');
      final deviceInfo = await _getDeviceInfo();

      print('📡 ApiService.login: Making POST request to /auth/login...');
      final response = await _dio.post('/auth/login', data: {
        'token': token,
        'device_info': deviceInfo,
      });

      print('📨 ApiService.login: Response received: ${response.statusCode}');
      print('📄 ApiService.login: Response data: ${response.data}');

      if (response.data['success']) {
        final data = response.data['data'];

        print('💾 ApiService.login: Saving tokens and user data...');
        // Save tokens
        await StorageService.saveAuthToken(data['jwt_token']);
        if (data['session_token'] != null) {
          await StorageService.saveSessionToken(data['session_token']);
        }

        // Save user data
        final user = User.fromJson(data['user']);
        await StorageService.saveUser(user);

        print('✅ ApiService.login: Login successful, returning response');
        return response.data;
      } else {
        print('❌ ApiService.login: API returned success=false: ${response.data['message']}');
        throw ApiException(response.data['message'] ?? 'Login failed');
      }
    } on DioException catch (e) {
      print('💥 ApiService.login: DioException occurred: ${e.message}');
      print('🔍 ApiService.login: Error type: ${e.type}');
      print('🔍 ApiService.login: Response: ${e.response?.data}');
      throw _handleDioError(e);
    }
  }

  Future<bool> validateToken() async {
    try {
      final response = await _dio.post('/auth/validate');
      return response.data['success'] == true;
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        return false;
      }
      throw _handleDioError(e);
    }
  }

  // User Profile
  Future<User> getUserProfile() async {
    try {
      final response = await _dio.get('/user/profile');
      
      if (response.data['success']) {
        final user = User.fromJson(response.data['data']);
        await StorageService.saveUser(user);
        return user;
      } else {
        throw ApiException(response.data['message'] ?? 'Failed to get profile');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // Courses
  Future<List<Course>> getUserCourses() async {
    try {
      final response = await _dio.get('/user/courses');
      
      if (response.data['success']) {
        final coursesData = response.data['data'] as List;
        return coursesData.map((courseJson) => Course.fromJson(courseJson)).toList();
      } else {
        throw ApiException(response.data['message'] ?? 'Failed to get courses');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  Future<Course> getCourseDetails(int courseId) async {
    try {
      final response = await _dio.get('/courses/$courseId');
      
      if (response.data['success']) {
        return Course.fromJson(response.data['data']);
      } else {
        throw ApiException(response.data['message'] ?? 'Failed to get course details');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // Video Progress
  Future<void> updateVideoProgress({
    required int videoId,
    required int watchTimeSeconds,
    required double completionPercentage,
  }) async {
    try {
      await _dio.post('/user/progress', data: {
        'video_id': videoId,
        'watch_time_seconds': watchTimeSeconds,
        'completion_percentage': completionPercentage,
      });
    } on DioException catch (e) {
      // Don't throw error for progress updates to avoid disrupting video playback
      print('Failed to update video progress: ${e.message}');
    }
  }

  Future<Map<String, dynamic>> getUserProgress() async {
    try {
      final response = await _dio.get('/user/progress');
      
      if (response.data['success']) {
        return response.data['data'];
      } else {
        throw ApiException(response.data['message'] ?? 'Failed to get progress');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // Video Watching
  Future<void> startWatchingVideo(int videoId) async {
    try {
      await _dio.post('/videos/watch', data: {
        'video_id': videoId,
        'action': 'start',
      });
    } on DioException catch (e) {
      print('Failed to log video start: ${e.message}');
    }
  }

  Future<void> pauseWatchingVideo(int videoId, int currentTime) async {
    try {
      await _dio.post('/videos/watch', data: {
        'video_id': videoId,
        'action': 'pause',
        'current_time': currentTime,
      });
    } on DioException catch (e) {
      print('Failed to log video pause: ${e.message}');
    }
  }

  Future<void> resumeWatchingVideo(int videoId, int currentTime) async {
    try {
      await _dio.post('/videos/watch', data: {
        'video_id': videoId,
        'action': 'resume',
        'current_time': currentTime,
      });
    } on DioException catch (e) {
      print('Failed to log video resume: ${e.message}');
    }
  }

  Future<void> completeWatchingVideo(int videoId, int totalTime) async {
    try {
      await _dio.post('/videos/watch', data: {
        'video_id': videoId,
        'action': 'complete',
        'total_time': totalTime,
      });
    } on DioException catch (e) {
      print('Failed to log video completion: ${e.message}');
    }
  }

  // Helper methods
  Future<String> _getDeviceFingerprint() async {
    String? fingerprint = StorageService.getDeviceFingerprint();

    if (fingerprint == null) {
      final deviceInfo = DeviceInfoPlugin();

      if (kIsWeb) {
        // Web platform
        final webInfo = await deviceInfo.webBrowserInfo;
        fingerprint = 'web_${webInfo.browserName}_${webInfo.userAgent?.hashCode ?? DateTime.now().millisecondsSinceEpoch}';
      } else {
        try {
          // Try to get platform-specific info
          final info = await deviceInfo.deviceInfo;
          if (info is AndroidDeviceInfo) {
            fingerprint = '${info.model}_${info.id}';
          } else if (info is IosDeviceInfo) {
            fingerprint = '${info.model}_${info.identifierForVendor}';
          } else {
            fingerprint = 'unknown_${DateTime.now().millisecondsSinceEpoch}';
          }
        } catch (e) {
          // Fallback for unsupported platforms
          fingerprint = 'fallback_${DateTime.now().millisecondsSinceEpoch}';
        }
      }

      await StorageService.saveDeviceFingerprint(fingerprint);
    }

    return fingerprint;
  }

  Future<Map<String, dynamic>> _getDeviceInfo() async {
    final deviceInfo = DeviceInfoPlugin();

    if (kIsWeb) {
      final webInfo = await deviceInfo.webBrowserInfo;
      return {
        'platform': 'web',
        'browser': webInfo.browserName?.name ?? 'unknown',
        'user_agent': webInfo.userAgent ?? 'Flutter Web',
        'vendor': webInfo.vendor ?? '',
      };
    } else {
      try {
        final info = await deviceInfo.deviceInfo;
        if (info is AndroidDeviceInfo) {
          return {
            'platform': 'android',
            'model': info.model,
            'version': info.version.release,
            'brand': info.brand,
          };
        } else if (info is IosDeviceInfo) {
          return {
            'platform': 'ios',
            'model': info.model,
            'version': info.systemVersion,
            'name': info.name,
          };
        } else {
          return {
            'platform': 'unknown',
            'type': 'mobile',
          };
        }
      } catch (e) {
        return {
          'platform': 'fallback',
          'error': e.toString(),
        };
      }
    }
  }

  void _handleUnauthorized() {
    // Clear auth data and redirect to login
    StorageService.clearAuthData();
    // Note: Navigation should be handled by the calling widget/service
  }

  ApiException _handleDioError(DioException e) {
    String message = 'An error occurred';
    
    if (e.response != null) {
      final data = e.response!.data;
      if (data is Map<String, dynamic> && data['message'] != null) {
        message = data['message'];
      } else {
        switch (e.response!.statusCode) {
          case 400:
            message = 'Bad request';
            break;
          case 401:
            message = 'Unauthorized';
            break;
          case 403:
            message = 'Forbidden';
            break;
          case 404:
            message = 'Not found';
            break;
          case 500:
            message = 'Server error';
            break;
          default:
            message = 'HTTP ${e.response!.statusCode}';
        }
      }
    } else if (e.type == DioExceptionType.connectionTimeout) {
      message = 'Connection timeout';
    } else if (e.type == DioExceptionType.receiveTimeout) {
      message = 'Receive timeout';
    } else if (e.type == DioExceptionType.connectionError) {
      message = 'Connection error';
    }
    
    return ApiException(message);
  }
}

class ApiException implements Exception {
  final String message;
  
  ApiException(this.message);
  
  @override
  String toString() => 'ApiException: $message';
}

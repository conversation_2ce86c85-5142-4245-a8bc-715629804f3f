import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';
import '../utils/web_utils.dart';

class AuthService extends ChangeNotifier {
  User? _user;
  bool _isAuthenticated = false;
  bool _isLoading = true;
  String? _error;

  // Getters
  User? get user => _user;
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;
  String? get error => _error;

  final ApiService _apiService = ApiService();

  AuthService() {
    _initializeAuth();
  }

  Future<void> _initializeAuth() async {
    print('🚀 AuthService._initializeAuth: Starting initialization...');
    _setLoading(true);

    try {
      // First, check for URL token (for authentication links)
      final urlToken = getUrlToken();
      print('🔍 AuthService._initializeAuth: URL token check - ${urlToken != null ? "Found token (${urlToken.length} chars)" : "No token found"}');

      // Always show debug info about URL token detection
      if (urlToken != null && urlToken.isNotEmpty) {
        _error = 'DEBUG: Found URL token (${urlToken.substring(0, 20)}...) - Processing...';
      } else {
        _error = 'DEBUG: No URL token found - Checking stored auth...';
      }
      notifyListeners();

      if (urlToken != null && urlToken.isNotEmpty) {
        print('🎫 AuthService._initializeAuth: Processing URL token...');

        // Add a small delay to show splash screen briefly for better UX
        await Future.delayed(const Duration(milliseconds: 500));

        print('📡 AuthService._initializeAuth: Calling login with URL token...');
        // Attempt login with URL token
        final success = await login(urlToken);
        print('📨 AuthService._initializeAuth: Login result: ${success ? "SUCCESS" : "FAILED"}');

        if (success) {
          print('✅ AuthService._initializeAuth: URL token login successful, clearing URL...');
          // Clear token from URL after successful login
          clearUrlToken();
          return; // Exit early, login method already sets loading to false
        } else {
          print('❌ AuthService._initializeAuth: URL token login failed, error: $_error');
        }
      }

      // Check if user data exists in storage
      final storedUser = StorageService.getUser();
      final token = StorageService.getAuthToken();

      if (storedUser != null && token != null) {
        // Validate token with server
        final isValid = await _apiService.validateToken();

        if (isValid) {
          _user = storedUser;
          _isAuthenticated = true;
          _error = null;

          // Refresh user data in background
          _refreshUserData();
        } else {
          // Token is invalid, clear stored data
          await _clearAuthData();
        }
      }
    } catch (e) {
      _error = e.toString();
      await _clearAuthData();
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> login(String token) async {
    print('🔐 AuthService.login: Starting login with token: ${token.substring(0, 20)}...');
    _setLoading(true);
    _error = 'DEBUG: Starting manual login process...';
    notifyListeners();

    try {
      print('📡 AuthService.login: Calling API service...');
      _error = 'DEBUG: Making API call to /auth/login...';
      notifyListeners();

      final response = await _apiService.login(token);
      print('📨 AuthService.login: API response received: ${response['success'] ? "SUCCESS" : "FAILED"}');

      if (response['success']) {
        print('✅ AuthService.login: Creating user from response...');
        _error = 'DEBUG: Login successful, processing user data...';
        notifyListeners();

        _user = User.fromJson(response['data']['user']);
        _isAuthenticated = true;

        print('💾 AuthService.login: Storing user data and tokens...');
        // Store user data and tokens
        await StorageService.saveUser(_user!);
        await StorageService.saveAuthToken(response['data']['jwt_token']);
        await StorageService.saveSessionToken(response['data']['session_token']);

        print('🔔 AuthService.login: Notifying listeners...');
        _error = null; // Clear debug message on success
        notifyListeners();
        print('🎉 AuthService.login: Login completed successfully!');
        return true;
      } else {
        _error = 'Login failed: ${response['message'] ?? 'Unknown error'}';
        print('❌ AuthService.login: Login failed: $_error');
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = 'Login error: ${e.toString()}';
      print('💥 AuthService.login: Exception occurred: $_error');
      notifyListeners();
      return false;
    } finally {
      _setLoading(false);
      print('🏁 AuthService.login: Setting loading to false');
    }
  }

  Future<void> logout() async {
    _setLoading(true);
    
    try {
      await _clearAuthData();
    } finally {
      _setLoading(false);
    }
  }

  Future<void> checkAuthStatus() async {
    if (!_isLoading) {
      _setLoading(true);
    }
    
    try {
      final token = StorageService.getAuthToken();
      
      if (token != null) {
        final isValid = await _apiService.validateToken();
        
        if (isValid) {
          final storedUser = StorageService.getUser();
          if (storedUser != null) {
            _user = storedUser;
            _isAuthenticated = true;
            _error = null;
            
            // Refresh user data
            await _refreshUserData();
          } else {
            await _clearAuthData();
          }
        } else {
          await _clearAuthData();
        }
      } else {
        await _clearAuthData();
      }
    } catch (e) {
      _error = e.toString();
      await _clearAuthData();
    } finally {
      _setLoading(false);
    }
  }

  Future<void> refreshUserProfile() async {
    if (!_isAuthenticated) return;
    
    try {
      final updatedUser = await _apiService.getUserProfile();
      _user = updatedUser;
      _error = null;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  Future<void> _refreshUserData() async {
    try {
      final updatedUser = await _apiService.getUserProfile();
      _user = updatedUser;
      notifyListeners();
    } catch (e) {
      // Don't update error state for background refresh failures
      print('Failed to refresh user data: $e');
    }
  }

  Future<void> _clearAuthData() async {
    await StorageService.clearAuthData();
    _user = null;
    _isAuthenticated = false;
    _error = null;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }



  // Helper methods
  bool get hasValidUser => _user != null && _isAuthenticated;
  
  String get userName => _user?.fullName ?? 'User';
  
  String get userEmail => _user?.email ?? '';
  
  String get userInitials => _user?.initials ?? 'U';
  
  double? get userBMI => _user?.bmi;
  
  String get userBMICategory => _user?.bmiCategory ?? 'Unknown';
  
  double? get weightToLose => _user?.weightToLose;
  
  bool get hasWeightGoal => _user?.targetWeight != null;
  
  // Progress tracking
  double get weightProgress {
    if (_user?.weight == null || _user?.targetWeight == null) return 0.0;
    
    final currentWeight = _user!.weight!;
    final targetWeight = _user!.targetWeight!;
    final startWeight = currentWeight; // This should ideally be stored separately
    
    if (startWeight <= targetWeight) return 100.0; // Already at or below target
    
    final totalWeightToLose = startWeight - targetWeight;
    final weightLost = startWeight - currentWeight;
    
    return (weightLost / totalWeightToLose * 100).clamp(0.0, 100.0);
  }
  
  String get weightProgressText {
    if (_user?.weight == null || _user?.targetWeight == null) {
      return 'No weight goal set';
    }
    
    final weightLost = weightToLose ?? 0;
    if (weightLost <= 0) {
      return 'Goal achieved!';
    }
    
    return '${weightLost.toStringAsFixed(1)} kg to go';
  }
}

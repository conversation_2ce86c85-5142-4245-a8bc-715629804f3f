import 'package:flutter/foundation.dart';
import 'storage_service.dart';
import '../models/course.dart';
import 'api_service.dart';

class VideoService extends ChangeNotifier {
  DateTime? _startDate;
  List<VideoWeek> _videoWeeks = [];
  List<Course> _courses = [];

  DateTime? get startDate => _startDate;
  List<VideoWeek> get videoWeeks => _videoWeeks;
  List<Course> get courses => _courses;

  VideoService() {
    _loadStartDate();
    _initializeFromCourses();
  }
  
  Future<void> _initializeFromCourses() async {
    try {
      // Initialize with fitness-focused video weeks
      _videoWeeks = [
        VideoWeek(
          weekNumber: 1,
          title: "Beginner Fitness Fundamentals",
          description: "Perfect for those starting their fitness journey. Learn basic exercises, proper form, and build a foundation for long-term success.",
          videos: [
            Video(id: 1, title: "Welcome to Your Fitness Journey", duration: "15:00", thumbnail: "assets/images/fitness1.jpg"),
            Video(id: 2, title: "Basic Bodyweight Exercises", duration: "25:00", thumbnail: "assets/images/fitness2.jpg"),
            Video(id: 3, title: "Proper Form and Safety", duration: "20:00", thumbnail: "assets/images/fitness3.jpg"),
          ],
        ),
        VideoWeek(
          weekNumber: 2,
          title: "Strength Training Mastery",
          description: "Build muscle, increase strength, and transform your physique with progressive resistance training techniques.",
          videos: [
            Video(id: 4, title: "Introduction to Resistance Training", duration: "22:00", thumbnail: "assets/images/strength1.jpg"),
            Video(id: 5, title: "Upper Body Strength Foundations", duration: "35:00", thumbnail: "assets/images/strength2.jpg"),
            Video(id: 6, title: "Lower Body Power Development", duration: "40:00", thumbnail: "assets/images/strength3.jpg"),
          ],
        ),
        VideoWeek(
          weekNumber: 3,
          title: "HIIT Cardio Bootcamp",
          description: "High-intensity interval training to burn fat, improve cardiovascular health, and boost metabolism.",
          videos: [
            Video(id: 7, title: "HIIT Training Principles", duration: "20:00", thumbnail: "assets/images/hiit1.jpg"),
            Video(id: 8, title: "Beginner HIIT Workout", duration: "30:00", thumbnail: "assets/images/hiit2.jpg"),
            Video(id: 9, title: "Intermediate Fat Burning Circuit", duration: "35:00", thumbnail: "assets/images/hiit3.jpg"),
          ],
        ),
        VideoWeek(
          weekNumber: 4,
          title: "Flexibility & Mobility",
          description: "Improve your range of motion, reduce injury risk, and enhance overall movement quality.",
          videos: [
            Video(id: 10, title: "Mobility Assessment", duration: "25:00", thumbnail: "assets/images/mobility1.jpg"),
            Video(id: 11, title: "Dynamic Warm-up Routine", duration: "15:00", thumbnail: "assets/images/mobility2.jpg"),
            Video(id: 12, title: "Static Stretching for Recovery", duration: "20:00", thumbnail: "assets/images/mobility3.jpg"),
          ],
        ),
      ];
      notifyListeners();
    } catch (e) {
      print('Error initializing video weeks: $e');
    }
  }

  // Method to update video weeks from API courses
  void updateFromCourses(List<Course> courses) {
    _courses = courses;
    _videoWeeks.clear();

    for (int i = 0; i < courses.length && i < 4; i++) {
      final course = courses[i];
      final videos = course.videos?.map((video) => Video(
        id: video.id,
        title: video.title,
        duration: "${video.durationMinutes}:00",
        thumbnail: video.thumbnail ?? "assets/images/default_video.jpg",
      )).toList() ?? [];

      _videoWeeks.add(VideoWeek(
        weekNumber: i + 1,
        title: course.title,
        description: course.description ?? '',
        videos: videos,
      ));
    }

    notifyListeners();
  }
  
  Future<void> _loadStartDate() async {
    final startDateString = await StorageService.getStartDate();
    if (startDateString != null) {
      _startDate = DateTime.parse(startDateString);
      notifyListeners();
    }
  }
  
  Future<void> startProgram() async {
    _startDate = DateTime.now();
    await StorageService.saveStartDate(_startDate!.toIso8601String());
    notifyListeners();
  }
  
  bool isWeekUnlocked(int weekNumber) {
    if (_startDate == null) return false;
    if (weekNumber == 1) return true; // First week is always unlocked
    
    final now = DateTime.now();
    final daysSinceStart = now.difference(_startDate!).inDays;
    final weeksUnlocked = (daysSinceStart ~/ 8) + 1; // Every 8 days unlocks a new week
    
    return weekNumber <= weeksUnlocked;
  }
  
  DateTime? getWeekUnlockDate(int weekNumber) {
    if (_startDate == null) return null;
    if (weekNumber == 1) return _startDate;
    
    return _startDate!.add(Duration(days: (weekNumber - 1) * 8));
  }
  
  int getCurrentWeek() {
    if (_startDate == null) return 0;
    
    final now = DateTime.now();
    final daysSinceStart = now.difference(_startDate!).inDays;
    return (daysSinceStart ~/ 8) + 1;
  }
  
  String getTimeUntilUnlock(int weekNumber) {
    final unlockDate = getWeekUnlockDate(weekNumber);
    if (unlockDate == null) return "Not available";
    
    final now = DateTime.now();
    if (now.isAfter(unlockDate)) return "Unlocked";
    
    final difference = unlockDate.difference(now);
    final days = difference.inDays;
    final hours = difference.inHours % 24;
    
    if (days > 0) {
      return "$days days, $hours hours";
    } else {
      return "$hours hours";
    }
  }
}

class VideoWeek {
  final int weekNumber;
  final String title;
  final String description;
  final List<Video> videos;
  
  VideoWeek({
    required this.weekNumber,
    required this.title,
    required this.description,
    required this.videos,
  });
}

class Video {
  final int id;
  final String title;
  final String duration;
  final String thumbnail;
  bool isWatched;
  
  Video({
    required this.id,
    required this.title,
    required this.duration,
    required this.thumbnail,
    this.isWatched = false,
  });
}

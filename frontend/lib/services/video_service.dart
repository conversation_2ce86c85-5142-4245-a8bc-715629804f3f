import 'package:flutter/foundation.dart';
import 'storage_service.dart';

class VideoService extends ChangeNotifier {
  DateTime? _startDate;
  List<VideoWeek> _videoWeeks = [];
  
  DateTime? get startDate => _startDate;
  List<VideoWeek> get videoWeeks => _videoWeeks;
  
  VideoService() {
    _initializeVideoWeeks();
    _loadStartDate();
  }
  
  void _initializeVideoWeeks() {
    _videoWeeks = [
      VideoWeek(
        weekNumber: 1,
        title: "Foundation Week",
        description: "Building healthy habits and understanding your journey",
        videos: [
          Video(id: 1, title: "Welcome & Introduction", duration: "5:30", thumbnail: "assets/images/video1.jpg"),
          Video(id: 2, title: "Setting Your Goals", duration: "8:15", thumbnail: "assets/images/video2.jpg"),
          Video(id: 3, title: "Understanding Nutrition", duration: "12:45", thumbnail: "assets/images/video3.jpg"),
        ],
      ),
      VideoWeek(
        weekNumber: 2,
        title: "Nutrition Fundamentals",
        description: "Learn the basics of healthy eating and meal planning",
        videos: [
          Video(id: 4, title: "Macronutrients Explained", duration: "10:20", thumbnail: "assets/images/video4.jpg"),
          Video(id: 5, title: "Meal Planning Basics", duration: "15:30", thumbnail: "assets/images/video5.jpg"),
          Video(id: 6, title: "Healthy Cooking Tips", duration: "18:45", thumbnail: "assets/images/video6.jpg"),
        ],
      ),
      VideoWeek(
        weekNumber: 3,
        title: "Exercise & Movement",
        description: "Introduction to effective workouts and staying active",
        videos: [
          Video(id: 7, title: "Beginner Workouts", duration: "20:15", thumbnail: "assets/images/video7.jpg"),
          Video(id: 8, title: "Cardio vs Strength", duration: "12:30", thumbnail: "assets/images/video8.jpg"),
          Video(id: 9, title: "Daily Movement Tips", duration: "8:45", thumbnail: "assets/images/video9.jpg"),
        ],
      ),
      VideoWeek(
        weekNumber: 4,
        title: "Mindset & Motivation",
        description: "Developing the right mindset for long-term success",
        videos: [
          Video(id: 10, title: "Overcoming Obstacles", duration: "14:20", thumbnail: "assets/images/video10.jpg"),
          Video(id: 11, title: "Building Confidence", duration: "11:15", thumbnail: "assets/images/video11.jpg"),
          Video(id: 12, title: "Maintaining Progress", duration: "16:30", thumbnail: "assets/images/video12.jpg"),
        ],
      ),
    ];
  }
  
  Future<void> _loadStartDate() async {
    final startDateString = await StorageService.getStartDate();
    if (startDateString != null) {
      _startDate = DateTime.parse(startDateString);
      notifyListeners();
    }
  }
  
  Future<void> startProgram() async {
    _startDate = DateTime.now();
    await StorageService.saveStartDate(_startDate!.toIso8601String());
    notifyListeners();
  }
  
  bool isWeekUnlocked(int weekNumber) {
    if (_startDate == null) return false;
    if (weekNumber == 1) return true; // First week is always unlocked
    
    final now = DateTime.now();
    final daysSinceStart = now.difference(_startDate!).inDays;
    final weeksUnlocked = (daysSinceStart ~/ 8) + 1; // Every 8 days unlocks a new week
    
    return weekNumber <= weeksUnlocked;
  }
  
  DateTime? getWeekUnlockDate(int weekNumber) {
    if (_startDate == null) return null;
    if (weekNumber == 1) return _startDate;
    
    return _startDate!.add(Duration(days: (weekNumber - 1) * 8));
  }
  
  int getCurrentWeek() {
    if (_startDate == null) return 0;
    
    final now = DateTime.now();
    final daysSinceStart = now.difference(_startDate!).inDays;
    return (daysSinceStart ~/ 8) + 1;
  }
  
  String getTimeUntilUnlock(int weekNumber) {
    final unlockDate = getWeekUnlockDate(weekNumber);
    if (unlockDate == null) return "Not available";
    
    final now = DateTime.now();
    if (now.isAfter(unlockDate)) return "Unlocked";
    
    final difference = unlockDate.difference(now);
    final days = difference.inDays;
    final hours = difference.inHours % 24;
    
    if (days > 0) {
      return "$days days, $hours hours";
    } else {
      return "$hours hours";
    }
  }
}

class VideoWeek {
  final int weekNumber;
  final String title;
  final String description;
  final List<Video> videos;
  
  VideoWeek({
    required this.weekNumber,
    required this.title,
    required this.description,
    required this.videos,
  });
}

class Video {
  final int id;
  final String title;
  final String duration;
  final String thumbnail;
  bool isWatched;
  
  Video({
    required this.id,
    required this.title,
    required this.duration,
    required this.thumbnail,
    this.isWatched = false,
  });
}

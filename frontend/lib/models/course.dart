class Course {
  final int id;
  final String title;
  final String? description;
  final int durationDays;
  final int videoUnlockInterval;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<CourseVideo> videos;

  Course({
    required this.id,
    required this.title,
    this.description,
    required this.durationDays,
    required this.videoUnlockInterval,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.videos = const [],
  });

  factory Course.fromJson(Map<String, dynamic> json) {
    return Course(
      id: json['id'] as int,
      title: json['title'] as String,
      description: json['description'] as String?,
      durationDays: json['duration_days'] as int? ?? 56,
      videoUnlockInterval: json['video_unlock_interval'] as int? ?? 8,
      isActive: json['is_active'] == 1 || json['is_active'] == true,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      videos: (json['videos'] as List<dynamic>?)
          ?.map((v) => CourseVideo.fromJson(v))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'duration_days': durationDays,
      'video_unlock_interval': videoUnlockInterval,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'videos': videos.map((v) => v.toJson()).toList(),
    };
  }

  Course copyWith({
    int? id,
    String? title,
    String? description,
    int? durationDays,
    int? videoUnlockInterval,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<CourseVideo>? videos,
  }) {
    return Course(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      durationDays: durationDays ?? this.durationDays,
      videoUnlockInterval: videoUnlockInterval ?? this.videoUnlockInterval,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      videos: videos ?? this.videos,
    );
  }

  // Helper methods
  int get totalVideos => videos.length;
  
  List<CourseVideo> get availableVideos {
    final now = DateTime.now();
    return videos.where((video) => video.isUnlocked(now)).toList();
  }
  
  CourseVideo? get nextVideo {
    final now = DateTime.now();
    final lockedVideos = videos.where((video) => !video.isUnlocked(now)).toList();
    if (lockedVideos.isEmpty) return null;
    
    lockedVideos.sort((a, b) => a.unlockDay.compareTo(b.unlockDay));
    return lockedVideos.first;
  }
  
  double get completionPercentage {
    if (videos.isEmpty) return 0.0;
    final completedVideos = videos.where((video) => video.isCompleted).length;
    return (completedVideos / videos.length) * 100;
  }
}

class CourseVideo {
  final int id;
  final int courseId;
  final String title;
  final String? description;
  final String vimeoVideoId;
  final String vimeoEmbedUrl;
  final int unlockDay;
  final int durationSeconds;
  final int orderIndex;
  final bool isActive;
  final DateTime createdAt;
  final VideoProgress? progress;

  CourseVideo({
    required this.id,
    required this.courseId,
    required this.title,
    this.description,
    required this.vimeoVideoId,
    required this.vimeoEmbedUrl,
    required this.unlockDay,
    required this.durationSeconds,
    required this.orderIndex,
    required this.isActive,
    required this.createdAt,
    this.progress,
  });

  factory CourseVideo.fromJson(Map<String, dynamic> json) {
    return CourseVideo(
      id: json['id'] as int,
      courseId: json['course_id'] as int,
      title: json['title'] as String,
      description: json['description'] as String?,
      vimeoVideoId: json['vimeo_video_id'] as String,
      vimeoEmbedUrl: json['vimeo_embed_url'] as String,
      unlockDay: json['unlock_day'] as int,
      durationSeconds: json['duration_seconds'] as int? ?? 0,
      orderIndex: json['order_index'] as int? ?? 0,
      isActive: json['is_active'] == 1 || json['is_active'] == true,
      createdAt: DateTime.parse(json['created_at']),
      progress: json['progress'] != null 
          ? VideoProgress.fromJson(json['progress'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'course_id': courseId,
      'title': title,
      'description': description,
      'vimeo_video_id': vimeoVideoId,
      'vimeo_embed_url': vimeoEmbedUrl,
      'unlock_day': unlockDay,
      'duration_seconds': durationSeconds,
      'order_index': orderIndex,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'progress': progress?.toJson(),
    };
  }

  // Helper methods
  bool isUnlocked(DateTime courseStartDate) {
    final unlockDate = courseStartDate.add(Duration(days: unlockDay));
    return DateTime.now().isAfter(unlockDate);
  }

  DateTime getUnlockDate(DateTime courseStartDate) {
    return courseStartDate.add(Duration(days: unlockDay));
  }

  bool get isCompleted => progress?.isCompleted ?? false;
  
  double get completionPercentage => progress?.completionPercentage ?? 0.0;
  
  int get watchTimeSeconds => progress?.watchTimeSeconds ?? 0;
  
  String get formattedDuration {
    final duration = Duration(seconds: durationSeconds);
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}

class VideoProgress {
  final int id;
  final int userId;
  final int videoId;
  final int watchTimeSeconds;
  final double completionPercentage;
  final DateTime lastWatchedAt;
  final bool isCompleted;

  VideoProgress({
    required this.id,
    required this.userId,
    required this.videoId,
    required this.watchTimeSeconds,
    required this.completionPercentage,
    required this.lastWatchedAt,
    required this.isCompleted,
  });

  factory VideoProgress.fromJson(Map<String, dynamic> json) {
    return VideoProgress(
      id: json['id'] as int,
      userId: json['user_id'] as int,
      videoId: json['video_id'] as int,
      watchTimeSeconds: json['watch_time_seconds'] as int? ?? 0,
      completionPercentage: double.parse(json['completion_percentage'].toString()),
      lastWatchedAt: DateTime.parse(json['last_watched_at']),
      isCompleted: json['is_completed'] == 1 || json['is_completed'] == true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'video_id': videoId,
      'watch_time_seconds': watchTimeSeconds,
      'completion_percentage': completionPercentage,
      'last_watched_at': lastWatchedAt.toIso8601String(),
      'is_completed': isCompleted,
    };
  }
}

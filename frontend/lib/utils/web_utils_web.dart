/// Web-specific implementation
import 'dart:html' as html;

// Global variable to store the token for debugging
String? _debugToken;

String? getUrlTokenImpl() {
  try {
    // Get the current URL directly from window.location.href
    final currentUrl = html.window.location.href;
    print('🔍 WEB_UTILS: Current URL: $currentUrl');

    // Parse the URL manually to extract the token
    if (currentUrl.contains('?token=')) {
      final tokenPart = currentUrl.split('?token=')[1];
      final token = tokenPart.split('&')[0]; // Handle any additional parameters

      print('🔍 WEB_UTILS: Token found: YES (${token.length} chars)');
      print('🎫 WEB_UTILS: Token preview: ${token.substring(0, 20)}...');

      // Store for debugging
      _debugToken = token;

      return token;
    } else {
      print('🔍 WEB_UTILS: Token found: NO (URL does not contain ?token=)');
      return null;
    }
  } catch (e) {
    print('❌ WEB_UTILS: Error getting URL token: $e');
    return null;
  }
}

void clearUrlTokenImpl() {
  try {
    final uri = Uri.parse(html.window.location.href);
    final newUri = uri.replace(queryParameters: {});
    html.window.history.replaceState(null, '', newUri.toString());
  } catch (e) {
    // Silently handle errors
  }
}

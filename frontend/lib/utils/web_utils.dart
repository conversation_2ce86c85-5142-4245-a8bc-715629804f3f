import 'package:flutter/foundation.dart';

// Conditional imports for web-specific functionality
import 'web_utils_stub.dart'
    if (dart.library.html) 'web_utils_web.dart';

/// Get token from URL parameters (web only)
String? getUrlToken() {
  if (kIsWeb) {
    return getUrlTokenImpl();
  }
  return null;
}

/// Clear token from URL after successful login (web only)
void clearUrlToken() {
  if (kIsWeb) {
    clearUrlTokenImpl();
  }
}

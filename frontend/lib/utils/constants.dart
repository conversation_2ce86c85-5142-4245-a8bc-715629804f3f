class Constants {
  // API Configuration
  static const String baseUrl = 'http://localhost:8000/api';
  static const String adminUrl = 'http://localhost:8000/admin';
  
  // Storage Keys
  static const String authTokenKey = 'auth_token';
  static const String userDataKey = 'user_data';
  static const String deviceFingerprintKey = 'device_fingerprint';
  static const String sessionTokenKey = 'session_token';
  
  // App Configuration
  static const String appName = 'KFT Fitness';
  static const String appVersion = '1.0.0';
  
  // Video Configuration
  static const int videoUnlockInterval = 8; // days
  static const int videoProgressUpdateInterval = 5; // seconds
  
  // UI Configuration
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double cardElevation = 2.0;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // Network Configuration
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  
  // Progress Tracking
  static const int progressUpdateInterval = 10; // seconds
  static const double videoCompletionThreshold = 0.9; // 90%
  
  // Notification Configuration
  static const String notificationChannelId = 'weight_loss_notifications';
  static const String notificationChannelName = 'Weight Loss Course';
  static const String notificationChannelDescription = 'Notifications for weight loss course updates';
}
